<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\MenuItemAddon;
use App\Models\MenuItemVariant;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class MenuDataTableController extends Controller
{
    /**
     * Display menus datatable
     */
    public function menusDataTable(Request $request)
    {
        if ($request->ajax()) {
            // For demo purposes, we'll create sample data if no user is authenticated
            // In production, you should always require authentication
            
            $menus = Menu::query();
            
            // If user is authenticated and has branch_id, filter by branch
            if (auth()->check() && auth()->user()->branch_id) {
                $menus = $menus->where('branch_id', auth()->user()->branch_id);
            }
            
            $menus = $menus->select('menus.*');

            return DataTables::of($menus)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="btn-group" role="group">';
                    $btn .= '<button type="button" class="btn btn-sm btn-info show-menu" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-warning edit-menu" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-danger delete-menu" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('Menu.menus');
    }

    /**
     * Display categories datatable
     */
    public function categoriesDataTable(Request $request)
    {
        if ($request->ajax()) {
            // For demo purposes, we'll create sample data if no user is authenticated
            // In production, you should always require authentication
            
            $categories = MenuCategory::query();
            
            // If user is authenticated and has branch_id, filter by branch
            if (auth()->check() && auth()->user()->branch_id) {
                $categories = $categories->whereHas('menu', function($query) {
                    $query->where('branch_id', auth()->user()->branch_id);
                });
            }
            
            $categories = $categories->with(['menu'])->select('menu_categories.*');

            return DataTables::of($categories)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="btn-group" role="group">';
                    $btn .= '<button type="button" class="btn btn-sm btn-info show-category" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-warning edit-category" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-danger delete-category" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('menu_name', function($row) {
                    return $row->menu ? $row->menu->name : '-';
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('Menu.categories');
    }

    /**
     * Display menu items datatable
     */
    public function menuItemsDataTable(Request $request)
    {
        if ($request->ajax()) {
            $menuItems = MenuItem::query();
            
            // If user is authenticated and has branch_id, filter by branch
            if (auth()->check() && auth()->user()->branch_id) {
                $menuItems = $menuItems->whereHas('category.menu', function($query) {
                    $query->where('branch_id', auth()->user()->branch_id);
                });
            }
            
            $menuItems = $menuItems->with(['category', 'category.menu'])->select('menu_items.*');

            return DataTables::of($menuItems)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="btn-group" role="group">';
                    $btn .= '<button type="button" class="btn btn-sm btn-info show-item" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-warning edit-item" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-danger delete-item" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->addColumn('image', function($row) {
                    if ($row->image) {
                        return '<img src="' . $row->image . '" class="menu-item-thumbnail" alt="' . $row->name . '" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">
                                <div class="menu-item-thumbnail-placeholder" style="display:none;"><i class="fas fa-utensils"></i></div>';
                    } else {
                        return '<div class="menu-item-thumbnail-placeholder"><i class="fas fa-utensils"></i></div>';
                    }
                })
                ->editColumn('category_name', function($row) {
                    return $row->category ? $row->category->name : '-';
                })
                ->editColumn('price', function($row) {
                    return number_format($row->base_price ?? $row->price, 2) . ' ر.س';
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'image', 'is_active'])
                ->make(true);
        }

        return view('Menu.menu-items');
    }

    /**
     * Display addons datatable
     */
    public function addonsDataTable(Request $request)
    {
        if ($request->ajax()) {
            $addons = MenuItemAddon::query();
            
            // If user is authenticated and has branch_id, filter by branch
            if (auth()->check() && auth()->user()->branch_id) {
                $addons = $addons->whereHas('menuItem.category.menu', function($query) {
                    $query->where('branch_id', auth()->user()->branch_id);
                });
            }
            
            $addons = $addons->with(['menuItem'])->select('menu_item_addons.*');

            return DataTables::of($addons)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="btn-group" role="group">';
                    $btn .= '<button type="button" class="btn btn-sm btn-info show-addon" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-warning edit-addon" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-danger delete-addon" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('menu_item_name', function($row) {
                    return $row->menuItem ? $row->menuItem->name : '-';
                })
                ->editColumn('price', function($row) {
                    return number_format($row->price, 2) . ' ر.س';
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('Menu.addons');
    }

    /**
     * Display variations datatable
     */
    public function variationsDataTable(Request $request)
    {
        if ($request->ajax()) {
            $variations = MenuItemVariant::query();
            
            // If user is authenticated and has branch_id, filter by branch
            if (auth()->check() && auth()->user()->branch_id) {
                $variations = $variations->where('branch_id', auth()->user()->branch_id);
            }
            
            $variations = $variations->with(['menuItem'])->select('menu_item_variants.*');

            return DataTables::of($variations)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="btn-group" role="group">';
                    $btn .= '<button type="button" class="btn btn-sm btn-info show-variation" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-warning edit-variation" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="btn btn-sm btn-danger delete-variation" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('menu_item_name', function($row) {
                    return $row->menuItem ? $row->menuItem->name : '-';
                })
                ->editColumn('price', function($row) {
                    return number_format($row->price, 2) . ' ر.س';
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? $row->created_at->format('Y-m-d H:i') : '-';
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('Menu.variations');
    }

    // CRUD Methods for Menus
    public function menusStore(Request $request)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $menu = Menu::create([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0,
            'branch_id' => auth()->user()->branch_id
        ]);

        return response()->json(['success' => true, 'message' => 'تم إضافة القائمة بنجاح']);
    }

    public function menusShow($id)
    {
        $menu = Menu::findOrFail($id);
        return response()->json($menu);
    }

    public function menusEdit($id)
    {
        $menu = Menu::findOrFail($id);
        return response()->json($menu);
    }

    public function menusUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $menu = Menu::findOrFail($id);
        $menu->update([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return response()->json(['success' => true, 'message' => 'تم تحديث القائمة بنجاح']);
    }

    public function menusDestroy($id)
    {
        $menu = Menu::findOrFail($id);
        $menu->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف القائمة بنجاح']);
    }

    // CRUD Methods for Categories
    public function categoriesStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_id' => 'required|exists:menus,id',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_id' => $request->menu_id,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('categories', 'public');
            $data['image'] = $imagePath;
        }

        $category = MenuCategory::create($data);

        return response()->json(['success' => true, 'message' => 'تم إضافة الفئة بنجاح']);
    }

    public function categoriesShow($id)
    {
        $category = MenuCategory::with('menu')->findOrFail($id);
        return response()->json($category);
    }

    public function categoriesEdit($id)
    {
        $category = MenuCategory::with('menu')->findOrFail($id);
        return response()->json($category);
    }

    public function categoriesUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_id' => 'required|exists:menus,id',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $category = MenuCategory::findOrFail($id);
        
        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_id' => $request->menu_id,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('categories', 'public');
            $data['image'] = $imagePath;
        }

        $category->update($data);

        return response()->json(['success' => true, 'message' => 'تم تحديث الفئة بنجاح']);
    }

    public function categoriesDestroy($id)
    {
        $category = MenuCategory::findOrFail($id);
        $category->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف الفئة بنجاح']);
    }

    // CRUD Methods for Menu Items
    public function menuItemsStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'category_id' => 'required|exists:menu_categories,id',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Get the menu_id from the selected category
        $category = MenuCategory::findOrFail($request->category_id);

        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_id' => $category->menu_id, // Add the required menu_id
            'category_id' => $request->category_id,
            'code' => $request->code ?? 'ITEM-' . time(), // Generate a code if not provided
            'base_price' => $request->price, // Use base_price instead of price
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('menu-items', 'public');
            $data['image'] = $imagePath; // Store as single string field
        }

        $menuItem = MenuItem::create($data);

        return response()->json(['success' => true, 'message' => 'تم إضافة عنصر القائمة بنجاح']);
    }

    public function menuItemsShow($id)
    {
        $menuItem = MenuItem::with(['category', 'category.menu'])->findOrFail($id);
        return response()->json($menuItem);
    }

    public function menuItemsEdit($id)
    {
        $menuItem = MenuItem::with(['category', 'category.menu'])->findOrFail($id);
        return response()->json($menuItem);
    }

    public function menuItemsUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'category_id' => 'required|exists:menu_categories,id',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $menuItem = MenuItem::findOrFail($id);
        
        // Get the menu_id from the selected category
        $category = MenuCategory::findOrFail($request->category_id);
        
        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_id' => $category->menu_id, // Update the menu_id
            'category_id' => $request->category_id,
            'base_price' => $request->price, // Use base_price instead of price
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('menu-items', 'public');
            $data['image'] = $imagePath; // Store as single string field
        }

        $menuItem->update($data);

        return response()->json(['success' => true, 'message' => 'تم تحديث عنصر القائمة بنجاح']);
    }

    public function menuItemsDestroy($id)
    {
        $menuItem = MenuItem::findOrFail($id);
        $menuItem->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف عنصر القائمة بنجاح']);
    }

    // CRUD Methods for Addons
    public function addonsStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_item_id' => 'required|exists:menu_items,id',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $addon = MenuItemAddon::create([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_item_id' => $request->menu_item_id,
            'price' => $request->price,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return response()->json(['success' => true, 'message' => 'تم إضافة الإضافة بنجاح']);
    }

    public function addonsShow($id)
    {
        $addon = MenuItemAddon::with('menuItem')->findOrFail($id);
        return response()->json($addon);
    }

    public function addonsEdit($id)
    {
        $addon = MenuItemAddon::with('menuItem')->findOrFail($id);
        return response()->json($addon);
    }

    public function addonsUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_item_id' => 'required|exists:menu_items,id',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $addon = MenuItemAddon::findOrFail($id);
        $addon->update([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_item_id' => $request->menu_item_id,
            'price' => $request->price,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return response()->json(['success' => true, 'message' => 'تم تحديث الإضافة بنجاح']);
    }

    public function addonsDestroy($id)
    {
        $addon = MenuItemAddon::findOrFail($id);
        $addon->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف الإضافة بنجاح']);
    }

    // CRUD Methods for Variations
    public function variationsStore(Request $request)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_item_id' => 'required|exists:menu_items,id',
            'price' => 'required|numeric|min:0',
            'type' => 'nullable|string|max:50',
            'color_code' => 'nullable|string|max:7',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_item_id' => $request->menu_item_id,
            'price' => $request->price,
            'type' => $request->type,
            'color_code' => $request->color_code,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0,
            'branch_id' => auth()->user()->branch_id
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('variations', 'public');
            $data['image'] = $imagePath;
        }

        $variation = MenuItemVariant::create($data);

        return response()->json(['success' => true, 'message' => 'تم إضافة المتغير بنجاح']);
    }

    public function variationsShow($id)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $variation = MenuItemVariant::where('branch_id', auth()->user()->branch_id)
                                   ->with('menuItem')
                                   ->findOrFail($id);
        return response()->json($variation);
    }

    public function variationsEdit($id)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $variation = MenuItemVariant::where('branch_id', auth()->user()->branch_id)
                                   ->with('menuItem')
                                   ->findOrFail($id);
        return response()->json($variation);
    }

    public function variationsUpdate(Request $request, $id)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'menu_item_id' => 'required|exists:menu_items,id',
            'price' => 'required|numeric|min:0',
            'type' => 'nullable|string|max:50',
            'color_code' => 'nullable|string|max:7',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $variation = MenuItemVariant::where('branch_id', auth()->user()->branch_id)->findOrFail($id);
        
        $data = [
            'name' => $request->name,
            'name_en' => $request->name_en,
            'menu_item_id' => $request->menu_item_id,
            'price' => $request->price,
            'type' => $request->type,
            'color_code' => $request->color_code,
            'is_active' => $request->is_active ?? 1,
            'sort_order' => $request->sort_order ?? 0
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('variations', 'public');
            $data['image'] = $imagePath;
        }

        $variation->update($data);

        return response()->json(['success' => true, 'message' => 'تم تحديث المتغير بنجاح']);
    }

    public function variationsDestroy($id)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        if (!auth()->user()->branch_id) {
            return response()->json(['error' => 'User not assigned to a branch'], 400);
        }

        $variation = MenuItemVariant::where('branch_id', auth()->user()->branch_id)->findOrFail($id);
        $variation->delete();
        return response()->json(['success' => true, 'message' => 'تم حذف المتغير بنجاح']);
    }

    // Additional methods for dropdowns and data fetching
    public function getMenusList()
    {
        $menus = Menu::where('is_active', 1)
            ->select('id', 'name', 'name_en')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
            
        return response()->json($menus);
    }

    public function getCategoriesList(Request $request)
    {
        $query = MenuCategory::where('is_active', 1)
            ->with('menu:id,name,name_en')
            ->select('id', 'name', 'name_en', 'menu_id')
            ->orderBy('sort_order')
            ->orderBy('name');
            
        // Filter by menu if provided
        if ($request->has('menu_id')) {
            $query->where('menu_id', $request->menu_id);
        }
        
        $categories = $query->get();
        
        return response()->json($categories);
    }

    public function getMenuItemsList(Request $request)
    {
        $query = MenuItem::where('is_active', 1);
            
        // Filter by branch if user is authenticated and has branch_id
        if (auth()->check() && auth()->user() && auth()->user()->branch_id) {
            $query->whereHas('category.menu', function($q) {
                $q->where('branch_id', auth()->user()->branch_id);
            });
        }
        
        $query = $query
            ->with(['category:id,name', 'category.menu:id,name'])
            ->select('id', 'name', 'category_id', 'base_price') // Changed price to base_price
            ->orderBy('sort_order')
            ->orderBy('name');
            
        // Filter by category if provided
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        
        // Filter by menu if provided
        if ($request->has('menu_id')) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('menu_id', $request->menu_id);
            });
        }
        
        $menuItems = $query->get();
        
        return response()->json($menuItems);
    }
}