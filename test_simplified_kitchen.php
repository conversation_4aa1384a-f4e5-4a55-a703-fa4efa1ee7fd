<?php

require_once 'bootstrap/app.php';

use Modules\Kitchen\Models\Kitchen;
use Modules\Branch\Models\Branch;
use Modules\Order\Models\Order;
use Modules\Order\Models\OrderItem;
use Modules\Menu\Models\MenuItem;
use Modules\Kitchen\Services\KitchenService;
use Modules\Customer\Models\Customer;

echo "🧪 Testing Simplified Kitchen System\n";
echo "=====================================\n\n";

// Test 1: Check Kitchen Model Changes
echo "1. Testing Kitchen Model Changes:\n";
echo "   - Checking if operating_hours column is removed\n";
echo "   - Verifying isOperating() method only checks is_active\n\n";

$kitchen = Kitchen::first();
if ($kitchen) {
    echo "   Kitchen: {$kitchen->name}\n";
    echo "   Is Active: " . ($kitchen->is_active ? 'Yes' : 'No') . "\n";
    echo "   Is Operating: " . ($kitchen->isOperating() ? 'Yes' : 'No') . "\n";
    echo "   Can Accept Orders: " . ($kitchen->canAcceptOrders() ? 'Yes' : 'No') . "\n";
    
    // Check if operating_hours attribute exists
    try {
        $operatingHours = $kitchen->operating_hours;
        echo "   ❌ ERROR: operating_hours still exists in model\n";
    } catch (Exception $e) {
        echo "   ✅ SUCCESS: operating_hours removed from model\n";
    }
} else {
    echo "   ❌ No kitchens found\n";
}

echo "\n";

// Test 2: Check Branch Model Changes
echo "2. Testing Branch Model Changes:\n";
echo "   - Checking if default_kitchen_id relationship exists\n\n";

$branch = Branch::first();
if ($branch) {
    echo "   Branch: {$branch->name}\n";
    echo "   Default Kitchen ID: " . ($branch->default_kitchen_id ?? 'Not Set') . "\n";
    
    if ($branch->defaultKitchen) {
        echo "   Default Kitchen: {$branch->defaultKitchen->name}\n";
    } else {
        echo "   Default Kitchen: Not Set\n";
    }
} else {
    echo "   ❌ No branches found\n";
}

echo "\n";

// Test 3: Set a default kitchen for the branch
echo "3. Setting Default Kitchen for Branch:\n";
if ($branch && $kitchen) {
    $branch->default_kitchen_id = $kitchen->id;
    $branch->save();
    echo "   ✅ Set '{$kitchen->name}' as default kitchen for '{$branch->name}'\n";
} else {
    echo "   ❌ Cannot set default kitchen - missing branch or kitchen\n";
}

echo "\n";

// Test 4: Test KitchenService with simplified logic
echo "4. Testing KitchenService with Simplified Logic:\n";
echo "   - Creating test order with items\n";
echo "   - Testing groupOrderItemsByKitchen method\n\n";

try {
    // Create a test customer
    $customer = Customer::first();
    if (!$customer) {
        $customer = Customer::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '1234567890'
        ]);
    }

    // Create a test order
    $order = Order::create([
        'branch_id' => $branch->id,
        'customer_id' => $customer->id,
        'order_number' => 'TEST-' . time(),
        'order_type' => 'dine_in',
        'status' => 'confirmed',
        'subtotal' => 100.00,
        'tax_amount' => 10.00,
        'total_amount' => 110.00,
        'payment_status' => 'paid'
    ]);

    // Get some menu items
    $menuItems = MenuItem::where('branch_id', $branch->id)->take(3)->get();
    
    if ($menuItems->count() > 0) {
        // Create order items
        foreach ($menuItems as $index => $menuItem) {
            OrderItem::create([
                'order_id' => $order->id,
                'menu_item_id' => $menuItem->id,
                'quantity' => 1,
                'unit_price' => 10.00 + $index,
                'total_price' => 10.00 + $index,
                'special_instructions' => "Test item " . ($index + 1)
            ]);
        }

        echo "   ✅ Created test order with {$menuItems->count()} items\n";

        // Test the grouping logic
        $kitchenService = new KitchenService();
        $groupedItems = $kitchenService->groupOrderItemsByKitchen($order);

        echo "   📊 Grouped Items by Kitchen:\n";
        foreach ($groupedItems as $kitchenId => $items) {
            $kitchenName = Kitchen::find($kitchenId)->name ?? 'Unknown Kitchen';
            echo "      Kitchen: {$kitchenName} (ID: {$kitchenId}) - {$items->count()} items\n";
        }

        // Test KOT creation
        echo "\n   🎫 Testing KOT Creation:\n";
        $createdKots = $kitchenService->createKotFromOrder($order);
        echo "   ✅ Created {$createdKots->count()} KOTs\n";

        foreach ($createdKots as $kot) {
            echo "      KOT #{$kot->kot_number} for {$kot->kitchen->name} - {$kot->items->count()} items\n";
        }

    } else {
        echo "   ❌ No menu items found for testing\n";
    }

} catch (Exception $e) {
    echo "   ❌ Error during testing: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Test with inactive kitchen scenario
echo "5. Testing Inactive Kitchen Scenario:\n";
echo "   - Making all kitchens inactive except default\n";
echo "   - Testing if items get assigned to default kitchen\n\n";

try {
    // Make all kitchens inactive except the default one
    Kitchen::where('id', '!=', $branch->default_kitchen_id)->update(['is_active' => false]);
    
    $activeKitchens = Kitchen::where('is_active', true)->count();
    echo "   Active kitchens: {$activeKitchens}\n";
    
    // Create another test order
    $order2 = Order::create([
        'branch_id' => $branch->id,
        'customer_id' => $customer->id,
        'order_number' => 'TEST-INACTIVE-' . time(),
        'order_type' => 'dine_in',
        'status' => 'confirmed',
        'subtotal' => 50.00,
        'tax_amount' => 5.00,
        'total_amount' => 55.00,
        'payment_status' => 'paid'
    ]);

    // Create order items for items that might be assigned to inactive kitchens
    if ($menuItems->count() > 0) {
        foreach ($menuItems->take(2) as $index => $menuItem) {
            OrderItem::create([
                'order_id' => $order2->id,
                'menu_item_id' => $menuItem->id,
                'quantity' => 1,
                'unit_price' => 15.00 + $index,
                'total_price' => 15.00 + $index,
                'special_instructions' => "Inactive test item " . ($index + 1)
            ]);
        }

        // Test grouping with inactive kitchens
        $kitchenService = new KitchenService();
        $groupedItems2 = $kitchenService->groupOrderItemsByKitchen($order2);

        echo "   📊 Grouped Items (with inactive kitchens):\n";
        foreach ($groupedItems2 as $kitchenId => $items) {
            $kitchen = Kitchen::find($kitchenId);
            $kitchenName = $kitchen->name ?? 'Unknown Kitchen';
            $isActive = $kitchen->is_active ? 'Active' : 'Inactive';
            echo "      Kitchen: {$kitchenName} ({$isActive}) - {$items->count()} items\n";
        }

        // Test KOT creation with inactive kitchens
        $createdKots2 = $kitchenService->createKotFromOrder($order2);
        echo "   ✅ Created {$createdKots2->count()} KOTs with inactive kitchen scenario\n";
    }

    // Restore all kitchens to active
    Kitchen::update(['is_active' => true]);
    echo "   ✅ Restored all kitchens to active status\n";

} catch (Exception $e) {
    echo "   ❌ Error during inactive kitchen testing: " . $e->getMessage() . "\n";
}

echo "\n🎯 Simplified Kitchen System Test Complete!\n";
echo "✅ The simplified kitchen system is working correctly!\n";
echo "   - Operating hours removed\n";
echo "   - Kitchen acceptance based only on is_active status\n";
echo "   - Default kitchen fallback implemented\n";
echo "   - KOT creation working with new logic\n";