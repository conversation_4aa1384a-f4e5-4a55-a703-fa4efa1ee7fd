<!-- Create Package Modal -->
<div class="modal fade" id="createPackageModal" tabindex="-1" role="dialog" aria-labelledby="createPackageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPackageModalLabel">إضافة باقة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="createPackageForm" action="{{ route('packages.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="create_package_name">اسم الباقة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="create_package_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="create_package_type">نوع الباقة <span class="text-danger">*</span></label>
                                <select class="form-control" id="create_package_type" name="type" required>
                                    <option value="">اختر نوع الباقة</option>
                                    <option value="basic">أساسية</option>
                                    <option value="standard">قياسية</option>
                                    <option value="premium">مميزة</option>
                                    <option value="enterprise">للشركات</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="create_package_price">السعر <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" id="create_package_price" name="price" required>
                                    <div class="input-group-append">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="create_billing_cycle">دورة الفوترة <span class="text-danger">*</span></label>
                                <select class="form-control" id="create_billing_cycle" name="billing_cycle" required>
                                    <option value="">اختر دورة الفوترة</option>
                                    <option value="monthly">شهرية</option>
                                    <option value="quarterly">ربع سنوية</option>
                                    <option value="yearly">سنوية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="create_package_description">الوصف</label>
                        <textarea class="form-control" id="create_package_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="create_max_branches">الحد الأقصى للفروع</label>
                                <input type="number" class="form-control" id="create_max_branches" name="max_branches" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="create_max_users">الحد الأقصى للمستخدمين</label>
                                <input type="number" class="form-control" id="create_max_users" name="max_users" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="create_storage_limit">حد التخزين (GB)</label>
                                <input type="number" step="0.1" class="form-control" id="create_storage_limit" name="storage_limit" min="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">الميزات المتاحة:</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_pos" name="has_pos" value="1">
                                <label class="form-check-label" for="create_has_pos">نقاط البيع</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_inventory" name="has_inventory" value="1">
                                <label class="form-check-label" for="create_has_inventory">إدارة المخزون</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_reports" name="has_reports" value="1">
                                <label class="form-check-label" for="create_has_reports">التقارير</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_delivery" name="has_delivery" value="1">
                                <label class="form-check-label" for="create_has_delivery">خدمة التوصيل</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_hr" name="has_hr" value="1">
                                <label class="form-check-label" for="create_has_hr">إدارة الموارد البشرية</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create_has_api" name="has_api" value="1">
                                <label class="form-check-label" for="create_has_api">واجهة برمجة التطبيقات</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="create_package_status">الحالة</label>
                                <select class="form-control" id="create_package_status" name="status">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="create_sort_order">ترتيب العرض</label>
                                <input type="number" class="form-control" id="create_sort_order" name="sort_order" value="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الباقة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Package Modal -->
<div class="modal fade" id="editPackageModal" tabindex="-1" role="dialog" aria-labelledby="editPackageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPackageModalLabel">تعديل الباقة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editPackageForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_package_name">اسم الباقة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_package_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_package_type">نوع الباقة <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_package_type" name="type" required>
                                    <option value="">اختر نوع الباقة</option>
                                    <option value="basic">أساسية</option>
                                    <option value="standard">قياسية</option>
                                    <option value="premium">مميزة</option>
                                    <option value="enterprise">للشركات</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_package_price">السعر <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" id="edit_package_price" name="price" required>
                                    <div class="input-group-append">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_billing_cycle">دورة الفوترة <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_billing_cycle" name="billing_cycle" required>
                                    <option value="">اختر دورة الفوترة</option>
                                    <option value="monthly">شهرية</option>
                                    <option value="quarterly">ربع سنوية</option>
                                    <option value="semi_annual">نصف سنوية</option>
                                    <option value="annual">سنوية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit_package_description">الوصف</label>
                        <textarea class="form-control" id="edit_package_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_max_branches">الحد الأقصى للفروع</label>
                                <input type="number" class="form-control" id="edit_max_branches" name="max_branches" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_max_users">الحد الأقصى للمستخدمين</label>
                                <input type="number" class="form-control" id="edit_max_users" name="max_users" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_storage_limit">حد التخزين (GB)</label>
                                <input type="number" step="0.1" class="form-control" id="edit_storage_limit" name="storage_limit" min="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">الميزات المتاحة:</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_pos" name="has_pos" value="1">
                                <label class="form-check-label" for="edit_has_pos">نقاط البيع</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_inventory" name="has_inventory" value="1">
                                <label class="form-check-label" for="edit_has_inventory">إدارة المخزون</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_reports" name="has_reports" value="1">
                                <label class="form-check-label" for="edit_has_reports">التقارير</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_delivery" name="has_delivery" value="1">
                                <label class="form-check-label" for="edit_has_delivery">خدمة التوصيل</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_hr" name="has_hr" value="1">
                                <label class="form-check-label" for="edit_has_hr">إدارة الموارد البشرية</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_has_api" name="has_api" value="1">
                                <label class="form-check-label" for="edit_has_api">واجهة برمجة التطبيقات</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="edit_package_status">الحالة</label>
                                <select class="form-control" id="edit_package_status" name="status">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="edit_sort_order">ترتيب العرض</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" value="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Package Modal -->
<div class="modal fade" id="showPackageModal" tabindex="-1" role="dialog" aria-labelledby="showPackageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showPackageModalLabel">تفاصيل الباقة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم الباقة:</strong></label>
                            <p id="show_package_name" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>نوع الباقة:</strong></label>
                            <p id="show_package_type" class="form-control-plaintext"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>السعر:</strong></label>
                            <p id="show_package_price" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>دورة الفوترة:</strong></label>
                            <p id="show_billing_cycle" class="form-control-plaintext"></p>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label><strong>الوصف:</strong></label>
                    <p id="show_package_description" class="form-control-plaintext"></p>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label><strong>الحد الأقصى للفروع:</strong></label>
                            <p id="show_max_branches" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label><strong>الحد الأقصى للمستخدمين:</strong></label>
                            <p id="show_max_users" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label><strong>حد التخزين:</strong></label>
                            <p id="show_storage_limit" class="form-control-plaintext"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label><strong>الميزات المتاحة:</strong></label>
                        <div id="show_features" class="mt-2"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_package_status" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>ترتيب العرض:</strong></label>
                            <p id="show_sort_order" class="form-control-plaintext"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ الإنشاء:</strong></label>
                            <p id="show_package_created_at" class="form-control-plaintext"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>آخر تحديث:</strong></label>
                            <p id="show_package_updated_at" class="form-control-plaintext"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="editPackageFromShow">تعديل</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Package Modal -->
<div class="modal fade" id="deletePackageModal" tabindex="-1" role="dialog" aria-labelledby="deletePackageModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePackageModalLabel">حذف الباقة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الباقة؟</p>
                <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                <div class="alert alert-info">
                    <strong>تفاصيل الباقة:</strong><br>
                    <span id="delete_package_details"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deletePackageForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف الباقة</button>
                </form>
            </div>
        </div>
    </div>
</div>