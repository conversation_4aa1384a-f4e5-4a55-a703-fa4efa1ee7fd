<?php

namespace Modules\Orders\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class POSController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display POS dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get recent orders for the current branch
        $recentOrders = Order::where('branch_id', $user->branch_id)
            ->with(['customer', 'table', 'items'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('orders::pos.index', compact('recentOrders'));
    }

    /**
     * Show the order creation form
     */
    public function create()
    {
        $user = Auth::user();
        
        // Get customers for the current branch/tenant
        $customers = Customer::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        // Get available tables for the current branch
        $tables = Table::where('branch_id', $user->branch_id)
            ->where('is_active', true)
            ->orderBy('table_number')
            ->get();

        // Get delivery personnel (users with delivery role)
        $deliveryPersonnel = User::where('tenant_id', $user->tenant_id)
            ->where('is_active', true)
            ->whereHas('roles', function($query) {
                $query->where('name', 'delivery');
            })
            // ->orWhere('position', 'like', '%delivery%')
            ->orderBy('name')
            ->get();

        // Get menu items for the current branch
        $menuItems = MenuItem::whereHas('menu', function($query) use ($user) {
                $query->where('branch_id', $user->branch_id)
                      ->where('is_active', true);
            })
            ->with(['category', 'variants', 'addons', 'menu'])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // If no menu items found, try to get all active menu items (fallback)
        if ($menuItems->isEmpty()) {
            $menuItems = MenuItem::with(['category', 'variants', 'addons', 'menu'])
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->limit(20) // Limit for demo purposes
                ->get();
        }

   

        // Group menu items by category name, handling null categories
        $menuCategories = $menuItems->groupBy(function($item) {
            return $item->category ? $item->category->name : 'Uncategorized';
        });

        return view('orders::pos.create', compact(
            'customers', 
            'tables', 
            'deliveryPersonnel', 
            'menuItems',
            'menuCategories'
        ));
    }

    /**
     * Store a new order from POS
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            \Log::info('POS Order Request:', $request->validated());
            $user = Auth::user();
            
            // Add branch and tenant info
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['cashier_id'] = $user->id;
            
            // Ensure order status is confirmed for KOT creation
            if (!isset($data['status']) || $data['status'] !== 'cancelled') {
                $data['status'] = 'confirmed';
            }

            // Create the order (KOT creation is handled automatically in OrderService)
            $order = $this->orderService->createOrder($data);

            // Check if KOT was created (for response message)
            $kotCreated = $order->hasActiveKot();

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully' . ($kotCreated ? ' with KOT orders' : ''),
                'data' => $order,
                'kot_created' => $kotCreated,
                'redirect' => route('pos.orders.kot', $order)
            ]);

        } catch (\Exception $e) {
            \Log::error('POS Order creation failed: ' . $e->getMessage(), [
                'request_data' => $request->validated(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate and display KOT
     */
    public function generateKOT(Order $order)
    {
        $user = Auth::user();
        
        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['items.menuItem', 'customer', 'table', 'branch']);

        return view('orders::pos.kot', compact('order'));
    }

    /**
     * Print KOT
     */
    public function printKOT(Order $order)
    {
        $user = Auth::user();
        
        // Verify order belongs to user's branch
        if ($order->branch_id !== $user->branch_id) {
            abort(404);
        }

        $order->load(['items.menuItem', 'customer', 'table', 'branch']);

        return view('orders::pos.kot-print', compact('order'));
    }

    /**
     * Get menu item variants and addons for AJAX requests
     */
    public function getMenuItemAddons(MenuItem $menuItem)
    {
        $user = Auth::user();

        // Verify menu item belongs to user's branch
        if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
            return response()->json(['error' => 'Menu item not found'], 404);
        }

        $menuItem->load(['variants', 'addons']);

        return response()->json([
            'variants' => $menuItem->variants,
            'addons' => $menuItem->addons
        ]);
    }
}
