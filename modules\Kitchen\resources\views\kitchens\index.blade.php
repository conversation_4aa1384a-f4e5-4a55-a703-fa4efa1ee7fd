@extends('layouts.master')

@section('title', 'Kitchen Management')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.kitchen-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.kitchen-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.station-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.workload-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.workload-low { background-color: #28a745; }
.workload-medium { background-color: #ffc107; }
.workload-high { background-color: #dc3545; }

.kitchen-stats {
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.operating-hours-display {
    font-size: 0.875rem;
    color: #6c757d;
}

.equipment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.equipment-tag {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
    color: #495057;
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Kitchen</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ Management</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary" id="add-kitchen-btn">
                <i class="mdi mdi-plus"></i> Add Kitchen
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-primary-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Total Kitchens</h6>
                            <h2 class="text-white mb-0 number-font" id="total-kitchens">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-utensils text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-success-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Active Kitchens</h6>
                            <h2 class="text-white mb-0 number-font" id="active-kitchens">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-check-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-warning-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Operating Now</h6>
                            <h2 class="text-white mb-0 number-font" id="operating-kitchens">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-clock text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-info-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Active KOTs</h6>
                            <h2 class="text-white mb-0 number-font" id="active-kots">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-receipt text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kitchens Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Kitchen Management</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table key-buttons text-md-nowrap" id="kitchens-table" data-page-length='25'>
                    <thead>
                        <tr>
                            <th class="border-bottom-0">#</th>
                            <th class="border-bottom-0">Name</th>
                            <th class="border-bottom-0">Code</th>
                            <th class="border-bottom-0">Station Type</th>
                            <th class="border-bottom-0">Branch</th>
                            <th class="border-bottom-0">Manager</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Operating</th>
                            <th class="border-bottom-0">Workload</th>
                            <th class="border-bottom-0">Created</th>
                            <th class="border-bottom-0">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Kitchen Modal -->
<div class="modal fade" id="addKitchenModal" tabindex="-1" role="dialog" aria-labelledby="addKitchenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addKitchenModalLabel">Add New Kitchen</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addKitchenForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch_id">Branch <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="branch_id" name="branch_id" required>
                                    <option value="">Select Branch</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Kitchen Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="code">Kitchen Code</label>
                                <input type="text" class="form-control" id="code" name="code">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="station_type">Station Type <span class="text-danger">*</span></label>
                                <select class="form-control" id="station_type" name="station_type" required>
                                    <option value="">Select Station Type</option>
                                    <option value="hot">Hot Station</option>
                                    <option value="cold">Cold Station</option>
                                    <option value="grill">Grill Station</option>
                                    <option value="fryer">Fryer Station</option>
                                    <option value="salad">Salad Station</option>
                                    <option value="dessert">Dessert Station</option>
                                    <option value="beverage">Beverage Station</option>
                                    <option value="prep">Prep Station</option>
                                    <option value="main">Main Kitchen</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_concurrent_orders">Max Concurrent Orders</label>
                                <input type="number" class="form-control" id="max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="average_prep_time_minutes">Avg Prep Time (minutes)</label>
                                <input type="number" class="form-control" id="average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="manager_id">Manager</label>
                                <select class="form-control select2" id="manager_id" name="manager_id">
                                    <option value="">Select Manager</option>
                                    @foreach($managers as $manager)
                                        <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="display_order">Display Order</label>
                                <input type="number" class="form-control" id="display_order" name="display_order" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Kitchen</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Kitchen Modal -->
<div class="modal fade" id="editKitchenModal" tabindex="-1" role="dialog" aria-labelledby="editKitchenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editKitchenModalLabel">Edit Kitchen</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editKitchenForm">
                <input type="hidden" id="edit_kitchen_id" name="kitchen_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_branch_id">Branch <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="edit_branch_id" name="branch_id" required>
                                    <option value="">Select Branch</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_name">Kitchen Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_code">Kitchen Code</label>
                                <input type="text" class="form-control" id="edit_code" name="code">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_station_type">Station Type <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_station_type" name="station_type" required>
                                    <option value="">Select Station Type</option>
                                    <option value="hot">Hot Station</option>
                                    <option value="cold">Cold Station</option>
                                    <option value="grill">Grill Station</option>
                                    <option value="fryer">Fryer Station</option>
                                    <option value="salad">Salad Station</option>
                                    <option value="dessert">Dessert Station</option>
                                    <option value="beverage">Beverage Station</option>
                                    <option value="prep">Prep Station</option>
                                    <option value="main">Main Kitchen</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="edit_description">Description</label>
                                <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_max_concurrent_orders">Max Concurrent Orders</label>
                                <input type="number" class="form-control" id="edit_max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_average_prep_time_minutes">Avg Prep Time (minutes)</label>
                                <input type="number" class="form-control" id="edit_average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_manager_id">Manager</label>
                                <select class="form-control select2" id="edit_manager_id" name="manager_id">
                                    <option value="">Select Manager</option>
                                    @foreach($managers as $manager)
                                        <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_display_order">Display Order</label>
                                <input type="number" class="form-control" id="edit_display_order" name="display_order" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                    <label class="form-check-label" for="edit_is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Kitchen</button>
                </div>
            </form>
        </div>
    </div>
</div>
@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#kitchens-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("kitchens.index") }}',
                type: 'GET'
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'name', name: 'name' },
                { data: 'code', name: 'code' },
                { data: 'station_type', name: 'station_type' },
                { data: 'branch_name', name: 'branch.name' },
                { data: 'manager_name', name: 'manager.name' },
                { data: 'status', name: 'is_active' },
                { data: 'operating_status', name: 'operating_status' },
                { data: 'workload', name: 'workload' },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[9, 'desc']] // Order by created_at descending
        });
    }

    // Initialize table
    initializeDataTable();

    // Add Kitchen Modal functionality
    $('#add-kitchen-btn').on('click', function() {
        $('#addKitchenModal').modal('show');
    });

    // Handle Add Kitchen Form Submission
    $('#addKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Creating...');

        const formData = new FormData(this);

        $.ajax({
            url: '{{ route("kitchens.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal("Success!", "Kitchen created successfully", "success");

                    // Reset form and close modal
                    $('#addKitchenForm')[0].reset();
                    $('#addKitchenModal').modal('hide');

                    // Refresh table
                    table.draw();
                } else {
                    swal("Error!", response.message || "Failed to create kitchen", "error");
                }
            },
            error: function(xhr) {
                console.error('Error creating kitchen:', xhr);
                let errorMessage = "Failed to create kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                swal("Error!", errorMessage, "error");
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Edit Kitchen functionality
    $(document).on('click', '.edit-kitchen', function() {
        const kitchenId = $(this).data('id');
        
        // Fetch kitchen data
        $.ajax({
            url: '{{ route("kitchens.show", ":id") }}'.replace(':id', kitchenId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const kitchen = response.data;
                    
                    // Populate edit form
                    $('#edit_kitchen_id').val(kitchen.id);
                    $('#edit_name').val(kitchen.name);
                    $('#edit_code').val(kitchen.code);
                    $('#edit_branch_id').val(kitchen.branch_id).trigger('change');
                    $('#edit_station_type').val(kitchen.station_type);
                    $('#edit_description').val(kitchen.description);
                    $('#edit_max_concurrent_orders').val(kitchen.max_concurrent_orders);
                    $('#edit_average_prep_time_minutes').val(kitchen.average_prep_time_minutes);
                    $('#edit_manager_id').val(kitchen.manager_id).trigger('change');
                    $('#edit_display_order').val(kitchen.display_order);
                    $('#edit_is_active').prop('checked', kitchen.is_active == 1);
                    
                    // Show modal
                    $('#editKitchenModal').modal('show');
                } else {
                    swal("Error!", "Failed to fetch kitchen data", "error");
                }
            },
            error: function(xhr) {
                console.error('Error fetching kitchen:', xhr);
                swal("Error!", "Failed to fetch kitchen data", "error");
            }
        });
    });

    // Handle Edit Kitchen Form Submission
    $('#editKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Updating...');

        const formData = new FormData(this);
        const kitchenId = $('#edit_kitchen_id').val();

        $.ajax({
            url: '{{ route("kitchens.update", ":id") }}'.replace(':id', kitchenId),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal("Success!", "Kitchen updated successfully", "success");

                    // Reset form and close modal
                    $('#editKitchenForm')[0].reset();
                    $('#editKitchenModal').modal('hide');

                    // Refresh table
                    table.draw();
                } else {
                    swal("Error!", response.message || "Failed to update kitchen", "error");
                }
            },
            error: function(xhr) {
                console.error('Error updating kitchen:', xhr);
                let errorMessage = "Failed to update kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                swal("Error!", errorMessage, "error");
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Delete Kitchen functionality
    $(document).on('click', '.delete-kitchen', function() {
        const kitchenId = $(this).data('id');
        const kitchenName = $(this).data('name');

        swal({
            title: "Are you sure?",
            text: `You want to delete kitchen "${kitchenName}"? This action cannot be undone!`,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel!",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: '{{ route("kitchens.destroy", ":id") }}'.replace(':id', kitchenId),
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            swal("Deleted!", "Kitchen has been deleted successfully", "success");
                            table.draw();
                        } else {
                            swal("Error!", response.message || "Failed to delete kitchen", "error");
                        }
                    },
                    error: function(xhr) {
                        console.error('Error deleting kitchen:', xhr);
                        let errorMessage = "Failed to delete kitchen";

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        swal("Error!", errorMessage, "error");
                    }
                });
            } else {
                swal("Cancelled", "Kitchen deletion cancelled", "error");
            }
        });
    });

    // Reset modals when closed
    $('#addKitchenModal').on('hidden.bs.modal', function() {
        $('#addKitchenForm')[0].reset();
        $('.select2').val(null).trigger('change');
    });

    $('#editKitchenModal').on('hidden.bs.modal', function() {
        $('#editKitchenForm')[0].reset();
        $('.select2').val(null).trigger('change');
    });
});
</script>
@endsection
