<?php

namespace Modules\Tenant\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Tenant\Services\SubscriptionService;
use Modules\Tenant\Http\Requests\CreateSubscriptionRequest;
use Modules\Tenant\Http\Requests\UpdateSubscriptionRequest;

class SubscriptionWebController extends Controller
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display a listing of subscriptions
     */
    public function index(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $planId = $request->get('plan_id');
            
            $query = TenantSubscription::with(['tenant', 'subscriptionPlan']);
            
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($planId) {
                $query->where('subscription_plan_id', $planId);
            }
            
            $subscriptions = $query->orderBy('created_at', 'desc')->paginate($perPage);
            $plans = \App\Models\SubscriptionPlan::all();
            
            // Get data for modals
            $tenants = \App\Models\Tenant::select('id', 'name', 'contact_email')->get();
            
            return view('tenant::subscriptions.index', compact('subscriptions', 'plans', 'tenants'));
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to retrieve subscriptions: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new subscription
     */
    public function create()
    {
        $tenants = Tenant::where('status', 'active')->get();
        $plans = SubscriptionPlan::where('is_active', true)->get();
        
        return view('tenant::subscriptions.create', compact('tenants', 'plans'));
    }

    /**
     * Store a newly created subscription
     */
    public function store(Request $request)
    {
        // Validate the request manually since the form fields don't match CreateSubscriptionRequest
        $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'plan_id' => 'required|exists:subscription_plans,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:start_date',
            'amount' => 'required|numeric|min:0',
            'status' => 'required|in:active,pending,suspended,cancelled',
            'payment_method' => 'nullable|string',
            'auto_renew' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();
            
            // Get the selected plan to determine billing cycle
            $plan = \App\Models\SubscriptionPlan::findOrFail($request->plan_id);
            
            // Map form data to service expected format
            $subscriptionData = [
                'tenant_id' => $request->tenant_id,
                'plan_id' => $request->plan_id,
                'billing_cycle' => $plan->billing_cycle ?? 'monthly', // Use plan's billing cycle
                'starts_at' => $request->start_date,
                'ends_at' => $request->end_date,
                'amount' => $request->amount,
                'status' => $request->status,
                'payment_method' => $request->payment_method,
                'auto_renew' => $request->auto_renew ?? false,
                'notes' => $request->notes,
            ];
            
            $subscription = $this->subscriptionService->createSubscription($subscriptionData);
            
            DB::commit();
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to create subscription: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified subscription
     */
    public function show(TenantSubscription $subscription)
    {
        try {
            $subscription->load(['tenant', 'subscriptionPlan']);
            
            if (request()->ajax()) {
                return response()->json($subscription);
            }
            
            return view('tenant::subscriptions.show', compact('subscription'));
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['error' => 'Subscription not found'], 404);
            }
            
            return back()->with('error', 'Failed to retrieve subscription: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified subscription
     */
    public function edit(TenantSubscription $subscription)
    {
        $tenants = Tenant::where('status', 'active')->get();
        $plans = SubscriptionPlan::where('is_active', true)->get();
        
        return view('tenant::subscriptions.edit', compact('subscription', 'tenants', 'plans'));
    }

    /**
     * Update the specified subscription
     */
    public function update(UpdateSubscriptionRequest $request, TenantSubscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $updatedSubscription = $this->subscriptionService->updateSubscription($subscription, $request->validated());
            
            DB::commit();
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update subscription: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel(TenantSubscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $cancelledSubscription = $this->subscriptionService->cancelSubscription($subscription);
            
            DB::commit();
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'Subscription cancelled successfully']);
            }
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription cancelled successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json(['error' => 'Failed to cancel subscription: ' . $e->getMessage()], 500);
            }
            
            return back()->with('error', 'Failed to cancel subscription: ' . $e->getMessage());
        }
    }

    /**
     * Suspend subscription
     */
    public function suspend(TenantSubscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $suspendedSubscription = $this->subscriptionService->suspendSubscription($subscription);
            
            DB::commit();
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'Subscription suspended successfully']);
            }
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription suspended successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json(['error' => 'Failed to suspend subscription: ' . $e->getMessage()], 500);
            }
            
            return back()->with('error', 'Failed to suspend subscription: ' . $e->getMessage());
        }
    }

    /**
     * Reactivate subscription
     */
    public function reactivate(TenantSubscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            $reactivatedSubscription = $this->subscriptionService->reactivateSubscription($subscription);
            
            DB::commit();
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'Subscription reactivated successfully']);
            }
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription reactivated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json(['error' => 'Failed to reactivate subscription: ' . $e->getMessage()], 500);
            }
            
            return back()->with('error', 'Failed to reactivate subscription: ' . $e->getMessage());
        }
    }

    /**
     * Show upgrade form
     */
    public function upgradeForm(TenantSubscription $subscription)
    {
        $plans = SubscriptionPlan::where('is_active', true)
            ->where('id', '!=', $subscription->plan_id)
            ->get();
            
        return view('tenant::subscriptions.upgrade', compact('subscription', 'plans'));
    }

    /**
     * Upgrade subscription
     */
    public function upgrade(Request $request, TenantSubscription $subscription)
    {
        $request->validate([
            'new_plan_id' => 'required|exists:subscription_plans,id',
            'upgrade_type' => 'required|in:immediate,next_billing',
            'prorate' => 'required|boolean',
            'payment_method' => 'nullable|string',
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            DB::beginTransaction();
            
            $upgradedSubscription = $this->subscriptionService->upgradeSubscription(
                $subscription, 
                $request->new_plan_id, 
                $request->upgrade_type
            );
            
            DB::commit();
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription upgraded successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to upgrade subscription: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Delete subscription
     */
    public function destroy(TenantSubscription $subscription)
    {
        try {
            DB::beginTransaction();
            
            // Check if subscription can be deleted
            if ($subscription->status === 'active') {
                return back()->with('error', 'Cannot delete an active subscription. Please cancel it first.');
            }
            
            $subscription->delete();
            
            DB::commit();
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'Subscription deleted successfully']);
            }
            
            return redirect()->route('subscriptions-web.index')
                ->with('success', 'Subscription deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json(['error' => 'Failed to delete subscription: ' . $e->getMessage()], 500);
            }
            
            return back()->with('error', 'Failed to delete subscription: ' . $e->getMessage());
        }
    }
}