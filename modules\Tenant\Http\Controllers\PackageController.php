<?php

namespace Modules\Tenant\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PackageController extends Controller
{
    /**
     * Display a listing of packages/subscription plans
     */
    public function index(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $type = $request->get('type');
            
            $query = SubscriptionPlan::query();
            
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($type) {
                $query->where('type', $type);
            }
            
            $packages = $query->orderBy('created_at', 'desc')->paginate($perPage);
            
            return view('tenant::packages.index', compact('packages'));
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to retrieve packages: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new package
     */
    public function create()
    {
        return view('tenant::packages.create');
    }

    /**
     * Store a newly created package
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly,quarterly',
            'max_branches' => 'nullable|integer|min:1',
            'max_users' => 'nullable|integer|min:1',
            'storage_limit' => 'nullable|numeric|min:0.1',
            'status' => 'required|in:1,0',
            'type' => 'required|in:basic,standard,premium,enterprise,custom'
        ]);

        try {
            DB::beginTransaction();
            
            // Prepare features array
            $features = [];
            if ($request->has_pos) $features[] = 'pos';
            if ($request->has_inventory) $features[] = 'inventory';
            if ($request->has_reports) $features[] = 'reports';
            if ($request->has_delivery) $features[] = 'delivery';
            if ($request->has_hr) $features[] = 'hr';
            if ($request->has_api) $features[] = 'api';
            
            // Prepare limitations array
            $limitations = [];
            if ($request->max_branches) $limitations['max_branches'] = (int)$request->max_branches;
            if ($request->max_users) $limitations['max_users'] = (int)$request->max_users;
            if ($request->storage_limit) $limitations['storage_limit'] = (float)$request->storage_limit;
            
            $package = SubscriptionPlan::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'billing_cycle' => $request->billing_cycle,
                'features' => $features,
                'limitations' => $limitations,
                'max_branches' => $request->max_branches,
                'max_users' => $request->max_users,
                'is_active' => (bool)$request->status,
                'sort_order' => $request->sort_order ?? 0,
            ]);
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to create package: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified package
     */
    public function show(SubscriptionPlan $package)
    {
        try {
            if (request()->ajax()) {
                return response()->json($package);
            }
            
            return view('tenant::packages.show', compact('package'));
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['error' => 'Package not found'], 404);
            }
            
            return back()->with('error', 'Failed to retrieve package: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified package
     */
    public function edit(SubscriptionPlan $package)
    {
        return view('tenant::packages.edit', compact('package'));
    }

    /**
     * Update the specified package
     */
    public function update(Request $request, SubscriptionPlan $package)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly,quarterly',
            'max_branches' => 'nullable|integer|min:1',
            'max_users' => 'nullable|integer|min:1',
            'storage_limit' => 'nullable|numeric|min:0.1',
            'status' => 'required|in:1,0',
            'type' => 'required|in:basic,standard,premium,enterprise,custom'
        ]);

        try {
            DB::beginTransaction();
            
            // Prepare features array
            $features = [];
            if ($request->has_pos) $features[] = 'pos';
            if ($request->has_inventory) $features[] = 'inventory';
            if ($request->has_reports) $features[] = 'reports';
            if ($request->has_delivery) $features[] = 'delivery';
            if ($request->has_hr) $features[] = 'hr';
            if ($request->has_api) $features[] = 'api';
            
            // Prepare limitations array
            $limitations = [];
            if ($request->max_branches) $limitations['max_branches'] = (int)$request->max_branches;
            if ($request->max_users) $limitations['max_users'] = (int)$request->max_users;
            if ($request->storage_limit) $limitations['storage_limit'] = (float)$request->storage_limit;
            
            $package->update([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'billing_cycle' => $request->billing_cycle,
                'features' => $features,
                'limitations' => $limitations,
                'max_branches' => $request->max_branches,
                'max_users' => $request->max_users,
                'is_active' => (bool)$request->status,
                'sort_order' => $request->sort_order ?? $package->sort_order ?? 0,
            ]);
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update package: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified package from storage
     */
    public function destroy(SubscriptionPlan $package)
    {
        try {
            DB::beginTransaction();
            
            // Check if package is being used by any active subscriptions
            $activeSubscriptions = $package->subscriptions()->where('status', 'active')->count();
            
            if ($activeSubscriptions > 0) {
                return back()->with('error', 'Cannot delete package that has active subscriptions');
            }
            
            $package->delete();
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to delete package: ' . $e->getMessage());
        }
    }

    /**
     * Toggle package status
     */
    public function toggleStatus(SubscriptionPlan $package)
    {
        try {
            DB::beginTransaction();
            
            $package->update([
                'status' => $package->status === 'active' ? 'inactive' : 'active'
            ]);
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package status updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update package status: ' . $e->getMessage());
        }
    }
}