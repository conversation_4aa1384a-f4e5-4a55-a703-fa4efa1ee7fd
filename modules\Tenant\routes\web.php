<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Tenant\Http\Controllers\TenantController;
use Modules\Tenant\Http\Controllers\SubscriptionController;
use Modules\Tenant\Http\Controllers\SubscriptionWebController;
use Modules\Tenant\Http\Controllers\PackageController;
use Modules\Tenant\Http\Controllers\TenantWebController;
use Modules\Tenant\Http\Controllers\BranchWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware('web')->group(function () {
    // Add your web routes here
});

// Branch switching for authenticated users
Route::middleware(['auth', 'web'])->group(function () {
    Route::post('/branch/switch/{branch}', [BranchWebController::class, 'switch'])->name('branch.switch');
});

// Tenant Management Web Routes
Route::prefix('admin/tenants')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [TenantWebController::class, 'index'])->name('tenants.index');
    Route::get('/create', [TenantWebController::class, 'create'])->name('tenants.create');
    Route::post('/', [TenantWebController::class, 'store'])->name('tenants.store');
    Route::get('/{tenant}', [TenantWebController::class, 'show'])->name('tenants.show');
    Route::get('/{tenant}/edit', [TenantWebController::class, 'edit'])->name('tenants.edit');
    Route::put('/{tenant}', [TenantWebController::class, 'update'])->name('tenants.update');
    Route::delete('/{tenant}', [TenantWebController::class, 'destroy'])->name('tenants.destroy');
    Route::post('/{tenant}/activate', [TenantWebController::class, 'activate'])->name('tenants.activate');
    Route::post('/{tenant}/deactivate', [TenantWebController::class, 'deactivate'])->name('tenants.deactivate');
    Route::post('/{tenant}/suspend', [TenantWebController::class, 'suspend'])->name('tenants.suspend');

    // DataTable AJAX endpoints
    Route::get('/data/list', [TenantWebController::class, 'getTenantsData'])->name('tenants.data');
    Route::get('/{tenant}/statistics', [TenantWebController::class, 'getStatistics'])->name('tenants.statistics');

    // Tenant subscription management
    Route::get('/{tenant}/subscription', [TenantWebController::class, 'subscription'])->name('tenants.subscription');
    Route::post('/{tenant}/subscription/update', [TenantWebController::class, 'updateSubscription'])->name('tenants.subscription.update');

    // Tenant billing
    Route::get('/{tenant}/billing', [TenantWebController::class, 'billing'])->name('tenants.billing');
    Route::get('/{tenant}/billing/data', [TenantWebController::class, 'getBillingData'])->name('tenants.billing.data');
});

// Branch Management Web Routes
Route::prefix('admin/branches')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [BranchWebController::class, 'index'])->name('branches.index');
    Route::get('/create', [BranchWebController::class, 'create'])->name('branches.create');
    Route::post('/', [BranchWebController::class, 'store'])->name('branches.store');
    Route::get('/{branch}', [BranchWebController::class, 'show'])->name('branches.show');
    Route::get('/{branch}/edit', [BranchWebController::class, 'edit'])->name('branches.edit');
    Route::put('/{branch}', [BranchWebController::class, 'update'])->name('branches.update');
    Route::delete('/{branch}', [BranchWebController::class, 'destroy'])->name('branches.destroy');

    // Branch status management
    Route::post('/{branch}/activate', [BranchWebController::class, 'activate'])->name('branches.activate');
    Route::post('/{branch}/deactivate', [BranchWebController::class, 'deactivate'])->name('branches.deactivate');

    // DataTable AJAX endpoints
    Route::get('/data/list', [BranchWebController::class, 'getBranchesData'])->name('branches.data');
    Route::get('/{branch}/statistics', [BranchWebController::class, 'getStatistics'])->name('branches.statistics');

    // Branch settings
    Route::get('/{branch}/settings', [BranchWebController::class, 'settings'])->name('branches.settings');
    Route::post('/{branch}/settings', [BranchWebController::class, 'updateSettings'])->name('branches.settings.update');

    // Branch users
    Route::get('/{branch}/users', [BranchWebController::class, 'users'])->name('branches.users');
    Route::get('/{branch}/users/data', [BranchWebController::class, 'getUsersData'])->name('branches.users.data');
});

// Subscription Management Web Routes
Route::prefix('admin/subscriptions')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [SubscriptionWebController::class, 'index'])->name('subscriptions.index');
    Route::get('/create', [SubscriptionWebController::class, 'create'])->name('subscriptions.create');
    Route::post('/', [SubscriptionWebController::class, 'store'])->name('subscriptions.store');
    Route::get('/{subscription}', [SubscriptionWebController::class, 'show'])->name('subscriptions.show');
    Route::get('/{subscription}/edit', [SubscriptionWebController::class, 'edit'])->name('subscriptions.edit');
    Route::put('/{subscription}', [SubscriptionWebController::class, 'update'])->name('subscriptions.update');
    Route::post('/{subscription}/cancel', [SubscriptionWebController::class, 'cancel'])->name('subscriptions.cancel');
    Route::post('/{subscription}/suspend', [SubscriptionWebController::class, 'suspend'])->name('subscriptions.suspend');
    Route::post('/{subscription}/reactivate', [SubscriptionWebController::class, 'reactivate'])->name('subscriptions.reactivate');
    Route::get('/{subscription}/upgrade', [SubscriptionWebController::class, 'upgradeForm'])->name('subscriptions.upgrade.form');
    Route::post('/{subscription}/upgrade', [SubscriptionWebController::class, 'upgrade'])->name('subscriptions.upgrade');
});

// Subscription API Routes (for AJAX calls)
Route::prefix('api/subscriptions')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [SubscriptionController::class, 'index'])->name('api.subscriptions.index');
    Route::post('/', [SubscriptionController::class, 'store'])->name('api.subscriptions.store');
    Route::get('/{subscription}', [SubscriptionController::class, 'show'])->name('api.subscriptions.show');
    Route::put('/{subscription}', [SubscriptionController::class, 'update'])->name('api.subscriptions.update');
    Route::post('/{subscription}/cancel', [SubscriptionController::class, 'cancel'])->name('api.subscriptions.cancel');
    Route::post('/{subscription}/suspend', [SubscriptionController::class, 'suspend'])->name('api.subscriptions.suspend');
    Route::post('/{subscription}/reactivate', [SubscriptionController::class, 'reactivate'])->name('api.subscriptions.reactivate');
    Route::post('/{subscription}/upgrade', [SubscriptionController::class, 'upgrade'])->name('api.subscriptions.upgrade');
    Route::get('/plans', [SubscriptionController::class, 'plans'])->name('api.subscriptions.plans');
});

// Subscription Web Management Routes (for web interface)
Route::prefix('admin/subscriptions-web')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [SubscriptionWebController::class, 'index'])->name('subscriptions-web.index');
    Route::get('/create', [SubscriptionWebController::class, 'create'])->name('subscriptions-web.create');
    Route::post('/', [SubscriptionWebController::class, 'store'])->name('subscriptions-web.store');
    Route::get('/{subscription}', [SubscriptionWebController::class, 'show'])->name('subscriptions-web.show');
    Route::get('/{subscription}/edit', [SubscriptionWebController::class, 'edit'])->name('subscriptions-web.edit');
    Route::put('/{subscription}', [SubscriptionWebController::class, 'update'])->name('subscriptions-web.update');
    Route::delete('/{subscription}', [SubscriptionWebController::class, 'destroy'])->name('subscriptions-web.destroy');
    Route::post('/{subscription}/cancel', [SubscriptionWebController::class, 'cancel'])->name('subscriptions-web.cancel');
    Route::post('/{subscription}/suspend', [SubscriptionWebController::class, 'suspend'])->name('subscriptions-web.suspend');
    Route::post('/{subscription}/reactivate', [SubscriptionWebController::class, 'reactivate'])->name('subscriptions-web.reactivate');
    Route::get('/{subscription}/upgrade', [SubscriptionWebController::class, 'upgradeForm'])->name('subscriptions-web.upgrade.form');
    Route::post('/{subscription}/upgrade', [SubscriptionWebController::class, 'upgrade'])->name('subscriptions-web.upgrade');
});

// Package Management Routes
Route::prefix('admin/packages')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [PackageController::class, 'index'])->name('packages.index');
    Route::get('/create', [PackageController::class, 'create'])->name('packages.create');
    Route::post('/', [PackageController::class, 'store'])->name('packages.store');
    Route::get('/{package}', [PackageController::class, 'show'])->name('packages.show');
    Route::get('/{package}/edit', [PackageController::class, 'edit'])->name('packages.edit');
    Route::put('/{package}', [PackageController::class, 'update'])->name('packages.update');
    Route::delete('/{package}', [PackageController::class, 'destroy'])->name('packages.destroy');
    Route::post('/{package}/toggle-status', [PackageController::class, 'toggleStatus'])->name('packages.toggle-status');
});

// Tenant Dashboard Routes (for individual tenants)
Route::prefix('tenant')->middleware(['auth', 'web', 'tenant'])->group(function () {
    Route::get('/dashboard', function () {
        return view('tenant::dashboard');
    })->name('tenant.dashboard');
    
    Route::get('/subscription', function () {
        return view('tenant::subscription');
    })->name('tenant.subscription');
    
    Route::get('/usage', function () {
        return view('tenant::usage');
    })->name('tenant.usage');
    
    Route::get('/billing', function () {
        return view('tenant::billing');
    })->name('tenant.billing');
});

// Public tenant routes (for subdomain access)
Route::domain('{subdomain}.'.config('app.domain', 'localhost'))->group(function () {
    Route::get('/', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.home', compact('tenant'));
    })->name('tenant.public.home');
    
    Route::get('/menu', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.menu', compact('tenant'));
    })->name('tenant.public.menu');
    
    Route::get('/contact', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.contact', compact('tenant'));
    })->name('tenant.public.contact');
});
