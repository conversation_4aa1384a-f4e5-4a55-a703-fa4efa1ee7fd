@extends('layouts.master')

@section('css')
<!-- DataTable css -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الباقات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createPackageModal">
                <i class="mdi mdi-plus"></i> إضافة باقة جديدة
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الباقات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع باقات الاشتراك</p>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-control select2" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control select2" id="type-filter">
                            <option value="">جميع الأنواع</option>
                            <option value="basic">أساسي</option>
                            <option value="premium">مميز</option>
                            <option value="enterprise">مؤسسي</option>
                            <option value="custom">مخصص</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-secondary" id="filter-btn">
                            <i class="mdi mdi-filter"></i> تصفية
                        </button>
                        <button type="button" class="btn btn-light" id="reset-filter">
                            <i class="mdi mdi-refresh"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="packages-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الباقة</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">السعر</th>
                                <th class="border-bottom-0">دورة الفوترة</th>
                                <th class="border-bottom-0">الوصف</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($packages as $package)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ $package->name }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($package->type == 'basic')
                                        <span class="badge badge-info">أساسي</span>
                                    @elseif($package->type == 'premium')
                                        <span class="badge badge-warning">مميز</span>
                                    @elseif($package->type == 'enterprise')
                                        <span class="badge badge-success">مؤسسي</span>
                                    @else
                                        <span class="badge badge-secondary">مخصص</span>
                                    @endif
                                </td>
                                <td>{{ number_format($package->price, 2) }} ر.س</td>
                                <td>
                                    @if($package->billing_cycle == 'monthly')
                                        شهري
                                    @elseif($package->billing_cycle == 'yearly')
                                        سنوي
                                    @else
                                        مدى الحياة
                                    @endif
                                </td>
                                <td>
                                    <span class="text-truncate" style="max-width: 200px; display: inline-block;" title="{{ $package->description }}">
                                        {{ Str::limit($package->description, 50) }}
                                    </span>
                                </td>
                                <td>
                                    @if($package->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-secondary">غير نشط</span>
                                    @endif
                                </td>
                                <td>{{ $package->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" onclick="showPackage({{ $package->id }})" title="عرض">
                                            <i class="mdi mdi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="editPackage({{ $package->id }})" title="تعديل">
                                            <i class="mdi mdi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm {{ $package->status == 'active' ? 'btn-warning' : 'btn-success' }}" onclick="togglePackageStatus({{ $package->id }})" title="{{ $package->status == 'active' ? 'إلغاء التفعيل' : 'تفعيل' }}">
                                            @if($package->status == 'active')
                                                <i class="mdi mdi-pause"></i>
                                            @else
                                                <i class="mdi mdi-play"></i>
                                            @endif
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="deletePackage({{ $package->id }}, '{{ $package->name }}')" title="حذف">
                                            <i class="mdi mdi-delete"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $packages->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->
@endsection

@section('js')
<!-- Internal Data tables -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>

@include('tenant::packages.modals')

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });
    
    // Initialize DataTable
    $('#packages-table').DataTable({
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        "order": [[ 0, "desc" ]]
    });
    
    // Filter functionality
    $('#filter-btn').click(function() {
        var status = $('#status-filter').val();
        var type = $('#type-filter').val();
        
        var url = new URL(window.location.href);
        if (status) {
            url.searchParams.set('status', status);
        } else {
            url.searchParams.delete('status');
        }
        
        if (type) {
            url.searchParams.set('type', type);
        } else {
            url.searchParams.delete('type');
        }
        
        window.location.href = url.toString();
    });
    
    // Reset filter
    $('#reset-filter').click(function() {
        var url = new URL(window.location.href);
        url.searchParams.delete('status');
        url.searchParams.delete('type');
        window.location.href = url.toString();
    });
});

function showPackage(id) {
    $.get(`/admin/packages/${id}`, function(data) {
        $('#show_package_name').text(data.name);
        $('#show_package_type').text(getPackageTypeText(data.type));
        $('#show_package_price').text(data.price + ' ر.س');
        $('#show_billing_cycle').text(getBillingCycleText(data.billing_cycle));
        $('#show_package_description').text(data.description || 'لا يوجد وصف');
        $('#show_max_branches').text(data.max_branches || 'غير محدود');
        $('#show_max_users').text(data.max_users || 'غير محدود');
        $('#show_storage_limit').text(data.storage_limit ? data.storage_limit + ' GB' : 'غير محدود');
        $('#show_package_status').html(data.status ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-secondary">غير نشط</span>');
        $('#show_sort_order').text(data.sort_order);
        $('#show_package_created_at').text(new Date(data.created_at).toLocaleDateString('ar-SA'));
        $('#show_package_updated_at').text(new Date(data.updated_at).toLocaleDateString('ar-SA'));
        
        // Show features
        var features = [];
        if (data.has_pos) features.push('<span class="badge badge-primary">نقاط البيع</span>');
        if (data.has_inventory) features.push('<span class="badge badge-info">إدارة المخزون</span>');
        if (data.has_reports) features.push('<span class="badge badge-success">التقارير</span>');
        if (data.has_delivery) features.push('<span class="badge badge-warning">خدمة التوصيل</span>');
        if (data.has_hr) features.push('<span class="badge badge-secondary">إدارة الموارد البشرية</span>');
        if (data.has_api) features.push('<span class="badge badge-dark">واجهة برمجة التطبيقات</span>');
        
        $('#show_features').html(features.length > 0 ? features.join(' ') : 'لا توجد ميزات محددة');
        
        $('#showPackageModal').modal('show');
    });
}

function editPackage(id) {
    $.get(`/admin/packages/${id}`, function(data) {
        $('#editPackageForm').attr('action', `/admin/packages/${id}`);
        $('#edit_package_name').val(data.name);
        $('#edit_package_code').val(data.code);
        $('#edit_package_type').val(data.type);
        $('#edit_package_price').val(data.price);
        $('#edit_billing_cycle').val(data.billing_cycle);
        $('#edit_package_description').val(data.description);
        $('#edit_max_branches').val(data.max_branches);
        $('#edit_max_users').val(data.max_users);
        $('#edit_storage_limit').val(data.storage_limit);
        $('#edit_has_pos').prop('checked', data.has_pos);
        $('#edit_has_inventory').prop('checked', data.has_inventory);
        $('#edit_has_reports').prop('checked', data.has_reports);
        $('#edit_has_delivery').prop('checked', data.has_delivery);
        $('#edit_has_hr').prop('checked', data.has_hr);
        $('#edit_has_api').prop('checked', data.has_api);
        $('#edit_package_status').val(data.status);
        $('#edit_sort_order').val(data.sort_order);
        
        $('#editPackageModal').modal('show');
    });
}

function deletePackage(id, name) {
    $('#delete_package_details').text(name);
    $('#deletePackageForm').attr('action', `/admin/packages/${id}`);
    $('#deletePackageModal').modal('show');
}

function togglePackageStatus(id) {
    if (confirm('هل أنت متأكد من تغيير حالة هذه الباقة؟')) {
        $.ajax({
            url: `/admin/packages/${id}/toggle-status`,
            type: 'PATCH',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                location.reload();
            }
        });
    }
}

function getPackageTypeText(type) {
    const typeMap = {
        'basic': 'أساسية',
        'standard': 'قياسية',
        'premium': 'مميزة',
        'enterprise': 'للشركات'
    };
    return typeMap[type] || type;
}

function getBillingCycleText(cycle) {
    const cycleMap = {
        'monthly': 'شهرية',
        'quarterly': 'ربع سنوية',
        'semi_annual': 'نصف سنوية',
        'annual': 'سنوية'
    };
    return cycleMap[cycle] || cycle;
}

// Edit from show modal
$('#editPackageFromShow').click(function() {
    $('#showPackageModal').modal('hide');
    // Get the package ID from the show modal and trigger edit
    var packageId = $('#showPackageModal').data('package-id');
    if (packageId) {
        editPackage(packageId);
    }
});
</script>
@endsection