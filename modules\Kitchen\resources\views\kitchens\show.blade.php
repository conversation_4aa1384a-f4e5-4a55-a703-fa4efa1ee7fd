@extends('layouts.master')

@section('title', 'Kitchen Details')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Kitchen</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ {{ $kitchen->name }}</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('kitchens.edit', $kitchen) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Kitchen
            </a>
            <a href="{{ route('kitchens.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Kitchens
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Kitchen Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Kitchen Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $kitchen->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Code:</strong></td>
                                    <td><span class="badge badge-info">{{ $kitchen->code }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Station Type:</strong></td>
                                    <td>{{ ucfirst($kitchen->station_type) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Branch:</strong></td>
                                    <td>{{ $kitchen->branch?->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Manager:</strong></td>
                                    <td>{{ $kitchen->manager?->name ?? 'Unassigned' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $kitchen->is_active ? 'success' : 'danger' }}">
                                            {{ $kitchen->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Operating:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $kitchen->isOperating() ? 'success' : 'warning' }}">
                                            {{ $kitchen->isOperating() ? 'Operating' : 'Closed' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Orders:</strong></td>
                                    <td>{{ $kitchen->max_concurrent_orders ?? '∞' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Workload:</strong></td>
                                    <td>{{ $kitchen->getCurrentWorkload() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $kitchen->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    @if($kitchen->description)
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <h6>Description:</h6>
                            <p>{{ $kitchen->description }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Stats</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ $kitchen->menuItems->count() }}</h4>
                                <small class="text-muted">Menu Items</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning">{{ $kitchen->kotOrders->count() }}</h4>
                                <small class="text-muted">Active KOTs</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Items -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Assigned Menu Items</h3>
                    <div class="card-options">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#assignMenuItemModal">
                            <i class="fas fa-plus"></i> Assign Menu Item
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="menu-items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Menu Item</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Assigned Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($kitchen->menuItems as $kitchenMenuItem)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $kitchenMenuItem->menuItem->name }}</td>
                                    <td>{{ $kitchenMenuItem->menuItem->category->name ?? 'N/A' }}</td>
                                    <td>${{ number_format($kitchenMenuItem->menuItem->price, 2) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $kitchenMenuItem->is_active ? 'success' : 'danger' }}">
                                            {{ $kitchenMenuItem->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>{{ $kitchenMenuItem->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-menu-item" 
                                                data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent KOT Orders -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent KOT Orders</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KOT #</th>
                                    <th>Order #</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Items</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($kitchen->kotOrders as $kotOrder)
                                <tr>
                                    <td>{{ $kotOrder->kot_number }}</td>
                                    <td>{{ $kotOrder->order->order_number }}</td>
                                    <td>
                                        <span class="badge badge-{{ $kotOrder->status == 'pending' ? 'warning' : ($kotOrder->status == 'preparing' ? 'info' : 'success') }}">
                                            {{ ucfirst($kotOrder->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $kotOrder->priority == 'urgent' ? 'danger' : ($kotOrder->priority == 'high' ? 'warning' : 'secondary') }}">
                                            {{ ucfirst($kotOrder->priority) }}
                                        </span>
                                    </td>
                                    <td>{{ $kotOrder->kotOrderItems->count() }}</td>
                                    <td>{{ $kotOrder->created_at->format('M d, H:i') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No recent KOT orders</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign Menu Item Modal -->
<div class="modal fade" id="assignMenuItemModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Menu Item</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="assignMenuItemForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="menu_item_id">Menu Item <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="menu_item_id" name="menu_item_id" required>
                            <option value="">Select Menu Item</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    // Initialize DataTable
    $('#menu-items-table').DataTable();

    // Load available menu items when modal opens
    $('#assignMenuItemModal').on('show.bs.modal', function() {
        $.ajax({
            url: '{{ route("kitchens.available-menu-items", $kitchen) }}',
            method: 'GET',
            success: function(response) {
                const select = $('#menu_item_id');
                select.empty().append('<option value="">Select Menu Item</option>');
                
                response.forEach(function(item) {
                    select.append(`<option value="${item.id}">${item.name} - ${item.category_name}</option>`);
                });
            }
        });
    });

    // Handle assign menu item form submission
    $('#assignMenuItemForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Assigning...');

        $.ajax({
            url: '{{ route("kitchens.assign-menu-item", $kitchen) }}',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    swal("Success!", "Menu item assigned successfully", "success").then(() => {
                        location.reload();
                    });
                } else {
                    swal("Error!", response.message || "Failed to assign menu item", "error");
                }
            },
            error: function(xhr) {
                let errorMessage = "Failed to assign menu item";
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                swal("Error!", errorMessage, "error");
            },
            complete: function() {
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle remove menu item
    $(document).on('click', '.remove-menu-item', function() {
        const menuItemId = $(this).data('menu-item-id');
        
        swal({
            title: "Are you sure?",
            text: "This will remove the menu item from this kitchen.",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, remove it!",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result) {
                $.ajax({
                    url: `{{ url('kitchens/' . $kitchen->id . '/menu-items') }}/${menuItemId}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            swal("Success!", "Menu item removed successfully", "success").then(() => {
                                location.reload();
                            });
                        } else {
                            swal("Error!", response.message || "Failed to remove menu item", "error");
                        }
                    },
                    error: function(xhr) {
                        swal("Error!", "Failed to remove menu item", "error");
                    }
                });
            }
        });
    });
});
</script>
@endsection