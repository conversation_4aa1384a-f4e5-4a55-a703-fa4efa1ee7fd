<?php

namespace Modules\Kitchen\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Services\KitchenService;
use Modules\Kitchen\Http\Requests\StoreKitchenRequest;
use Modules\Kitchen\Http\Requests\UpdateKitchenRequest;
use Modules\Kitchen\Http\Requests\AssignMenuItemRequest;
use App\Models\Branch;
use App\Models\User;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class KitchenController extends Controller
{
    public function __construct(
        private KitchenService $kitchenService
    ) {}

    /**
     * Display a listing of kitchens.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            $kitchens = Kitchen::with(['branch', 'manager'])
                ->select(['id', 'name', 'code', 'station_type', 'branch_id', 'manager_id', 'is_active', 'created_at']);

            return DataTables::of($kitchens)
                ->addColumn('branch_name', function ($kitchen) {
                    return $kitchen->branch?->name ?? 'N/A';
                })
                ->addColumn('manager_name', function ($kitchen) {
                    return $kitchen->manager?->name ?? 'Unassigned';
                })
                ->addColumn('status', function ($kitchen) {
                    $statusClass = $kitchen->is_active ? 'success' : 'danger';
                    $statusText = $kitchen->is_active ? 'Active' : 'Inactive';
                    return "<span class='badge badge-{$statusClass}'>{$statusText}</span>";
                })
                ->addColumn('operating_status', function ($kitchen) {
                    $isOperating = $kitchen->isOperating();
                    $statusClass = $isOperating ? 'success' : 'warning';
                    $statusText = $isOperating ? 'Operating' : 'Closed';
                    return "<span class='badge badge-{$statusClass}'>{$statusText}</span>";
                })
                ->addColumn('workload', function ($kitchen) {
                    $workload = $kitchen->getCurrentWorkload();
                    $maxOrders = $kitchen->max_concurrent_orders ?? '∞';
                    return "{$workload}/{$maxOrders}";
                })
                ->addColumn('actions', function ($kitchen) {
                    return view('kitchen::partials.kitchen-actions', compact('kitchen'))->render();
                })
                ->editColumn('created_at', function ($kitchen) {
                    return $kitchen->created_at->format('M d, Y H:i');
                })
                ->rawColumns(['status', 'operating_status', 'actions'])
                ->make(true);
        }

        $branches = Branch::active()->get();
        $managers = User::active()->get();

        return view('kitchen::kitchens.index', compact('branches', 'managers'));
    }

    /**
     * Show the form for creating a new kitchen.
     */
    public function create(): View
    {
        $branches = Branch::active()->get();
        $managers = User::active()->get();
        $stationTypes = [
            'hot' => 'Hot Station',
            'cold' => 'Cold Station',
            'grill' => 'Grill Station',
            'fryer' => 'Fryer Station',
            'salad' => 'Salad Station',
            'dessert' => 'Dessert Station',
            'beverage' => 'Beverage Station',
            'prep' => 'Prep Station',
            'main' => 'Main Kitchen',
            'other' => 'Other',
        ];

        return view('kitchen::kitchens.create', compact('branches', 'managers', 'stationTypes'));
    }

    /**
     * Store a newly created kitchen.
     */
    public function store(StoreKitchenRequest $request): JsonResponse
    {
        try {
            $kitchen = $this->kitchenService->createKitchen($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Kitchen created successfully.',
                'data' => $kitchen
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified kitchen.
     */
    public function show(Request $request, Kitchen $kitchen)
    {
        $kitchen->load(['branch', 'manager', 'menuItems.menuItem', 'kotOrders' => function ($query) {
            $query->whereIn('status', ['pending', 'preparing', 'ready'])
                  ->with(['order', 'kotOrderItems.menuItem'])
                  ->ordered()
                  ->limit(10);
        }]);

        // If it's an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $kitchen
            ]);
        }

        return view('kitchen::kitchens.show', compact('kitchen'));
    }

    /**
     * Show the form for editing the specified kitchen.
     */
    public function edit(Kitchen $kitchen): View
    {
        $branches = Branch::active()->get();
        $managers = User::active()->get();
        $stationTypes = [
            'hot' => 'Hot Station',
            'cold' => 'Cold Station',
            'grill' => 'Grill Station',
            'fryer' => 'Fryer Station',
            'salad' => 'Salad Station',
            'dessert' => 'Dessert Station',
            'beverage' => 'Beverage Station',
            'prep' => 'Prep Station',
            'main' => 'Main Kitchen',
            'other' => 'Other',
        ];

        return view('kitchen::kitchens.edit', compact('kitchen', 'branches', 'managers', 'stationTypes'));
    }

    /**
     * Update the specified kitchen.
     */
    public function update(UpdateKitchenRequest $request, Kitchen $kitchen): JsonResponse
    {
        try {
            $kitchen = $this->kitchenService->updateKitchen($kitchen, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Kitchen updated successfully.',
                'data' => $kitchen
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified kitchen.
     */
    public function destroy(Kitchen $kitchen): JsonResponse
    {
        try {
            $this->kitchenService->deleteKitchen($kitchen);

            return response()->json([
                'success' => true,
                'message' => 'Kitchen deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get kitchen menu items for DataTables.
     */
    public function menuItems(Request $request, Kitchen $kitchen): JsonResponse
    {
        if ($request->ajax()) {
            $menuItems = $kitchen->kitchenMenuItems()
                ->with(['menuItem.category'])
                ->select(['id', 'menu_item_id', 'prep_time_minutes', 'priority_level', 'is_active', 'assigned_at']);

            return DataTables::of($menuItems)
                ->addColumn('menu_item_name', function ($kitchenMenuItem) {
                    return $kitchenMenuItem->menuItem?->name ?? 'N/A';
                })
                ->addColumn('category_name', function ($kitchenMenuItem) {
                    return $kitchenMenuItem->menuItem?->category?->name ?? 'N/A';
                })
                ->addColumn('status', function ($kitchenMenuItem) {
                    $statusClass = $kitchenMenuItem->is_active ? 'success' : 'danger';
                    $statusText = $kitchenMenuItem->is_active ? 'Active' : 'Inactive';
                    return "<span class='badge badge-{$statusClass}'>{$statusText}</span>";
                })
                ->addColumn('actions', function ($kitchenMenuItem) use ($kitchen) {
                    return view('kitchen::partials.menu-item-actions', compact('kitchenMenuItem', 'kitchen'))->render();
                })
                ->editColumn('assigned_at', function ($kitchenMenuItem) {
                    return $kitchenMenuItem->assigned_at?->format('M d, Y H:i') ?? 'N/A';
                })
                ->rawColumns(['status', 'actions'])
                ->make(true);
        }

        return response()->json(['error' => 'Invalid request'], 400);
    }

    /**
     * Assign menu item to kitchen.
     */
    public function assignMenuItem(AssignMenuItemRequest $request, Kitchen $kitchen): JsonResponse
    {
        try {
            $assignment = $this->kitchenService->assignMenuItem(
                $kitchen->id,
                $request->menu_item_id,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Menu item assigned successfully.',
                'data' => $assignment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove menu item from kitchen.
     */
    public function removeMenuItem(Kitchen $kitchen, MenuItem $menuItem): JsonResponse
    {
        try {
            $this->kitchenService->removeMenuItem($kitchen->id, $menuItem->id);

            return response()->json([
                'success' => true,
                'message' => 'Menu item removed successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available menu items for assignment.
     */
    public function availableMenuItems(Kitchen $kitchen): JsonResponse
    {
        $assignedMenuItemIds = $kitchen->kitchenMenuItems()
            ->pluck('menu_item_id')
            ->toArray();

        $availableMenuItems = MenuItem::with('category')
            ->whereNotIn('id', $assignedMenuItemIds)
            ->active()
            ->get()
            ->map(function ($menuItem) {
                return [
                    'id' => $menuItem->id,
                    'name' => $menuItem->name,
                    'category' => $menuItem->category?->name ?? 'Uncategorized',
                    'price' => $menuItem->price,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $availableMenuItems
        ]);
    }
}
