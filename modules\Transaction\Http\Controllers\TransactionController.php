<?php

namespace Modules\Transaction\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StoreTransactionRequest;
use Modules\Transaction\Http\Requests\UpdateTransactionRequest;
use Modules\Transaction\Http\Resources\TransactionResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class TransactionController extends Controller
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of transactions.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Check if this is a DataTables request
            if ($request->has('draw')) {
                return $this->handleDataTablesRequest($request);
            }

            $filters = $request->only([
                'status', 'order_id', 'date_from', 'date_to', 'search',
                'sort_by', 'sort_direction', 'per_page'
            ]);

            $transactions = $this->transactionService->getAllTransactions($filters);

            return response()->json([
                'success' => true,
                'data' => TransactionResource::collection($transactions->items()),
                'meta' => [
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage(),
                    'per_page' => $transactions->perPage(),
                    'total' => $transactions->total(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transactions',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle DataTables server-side processing request.
     */
    private function handleDataTablesRequest(Request $request): JsonResponse
    {
        try {
            $draw = $request->get('draw');
            $start = $request->get('start', 0);
            $length = $request->get('length', 10);
            $search = $request->get('search.value', '');

            // Get order information
            $orderColumn = $request->get('order.0.column', 0);
            $orderDir = $request->get('order.0.dir', 'desc');

            // Column mapping for ordering
            $columns = ['id', 'transaction_number', 'order.order_number', 'status', 'total_amount', 'paid_amount', 'due_amount', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'created_at';

            $filters = [
                'status' => $request->get('status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $search,
                'sort_by' => $orderBy,
                'sort_direction' => $orderDir,
                'per_page' => $length,
                'page' => floor($start / $length) + 1,
            ];

            $transactions = $this->transactionService->getAllTransactions($filters);

            $data = [];
            foreach ($transactions->items() as $index => $transaction) {
                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'transaction_number' => $transaction->transaction_number,
                    'order_number' => $transaction->order ? $transaction->order->order_number : 'N/A',
                    'status_badge' => '<span class="transaction-status status-' . $transaction->status . '">' . ucfirst(str_replace('_', ' ', $transaction->status)) . '</span>',
                    'formatted_total' => '$' . number_format($transaction->total_amount, 2),
                    'formatted_paid' => '$' . number_format($transaction->paid_amount, 2),
                    'formatted_due' => '$' . number_format($transaction->due_amount, 2),
                    'formatted_date' => $transaction->created_at->format('M d, Y H:i'),
                    'action' => '<button class="btn btn-sm btn-primary select-transaction" data-transaction-id="' . $transaction->id . '">Select</button>',
                ];
            }

            return response()->json([
                'draw' => intval($draw),
                'recordsTotal' => $transactions->total(),
                'recordsFiltered' => $transactions->total(),
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'draw' => intval($request->get('draw', 1)),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Store a newly created transaction.
     */
    public function store(StoreTransactionRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            
            // Check if this is an order-based transaction or standalone
            if (!empty($validatedData['order_id'])) {
                // Order-based transaction
                $order = \App\Models\Order::findOrFail($validatedData['order_id']);
                $transaction = $this->transactionService->createTransactionFromOrder($order);
            } else {
                // Standalone transaction
                $transaction = $this->transactionService->createStandaloneTransaction($validatedData);
            }

            return response()->json([
                'success' => true,
                'message' => 'Transaction created successfully',
                'data' => new TransactionResource($transaction),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified transaction.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            return response()->json([
                'success' => true,
                'data' => new TransactionResource($transaction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified transaction.
     */
    public function update(UpdateTransactionRequest $request, int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            // Update transaction notes if provided
            if ($request->has('notes')) {
                $transaction->update([
                    'notes' => $request->notes,
                    'updated_by' => auth()->id(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Transaction updated successfully',
                'data' => new TransactionResource($transaction->fresh()),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified transaction (void).
     */
    public function destroy(int $id, Request $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $reason = $request->input('reason', 'Transaction voided by user');
            $voidedTransaction = $this->transactionService->voidTransaction($transaction, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Transaction voided successfully',
                'data' => new TransactionResource($voidedTransaction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to void transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get transaction statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['date_from', 'date_to']);
            $statistics = $this->transactionService->getTransactionStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get due transactions.
     */
    public function due(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['limit']);
            $dueTransactions = $this->transactionService->getDueTransactions($filters);

            return response()->json([
                'success' => true,
                'data' => TransactionResource::collection($dueTransactions),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve due transactions',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update transaction status.
     */
    public function updateStatus(int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $updatedTransaction = $this->transactionService->updateTransactionStatus($transaction);

            return response()->json([
                'success' => true,
                'message' => 'Transaction status updated successfully',
                'data' => new TransactionResource($updatedTransaction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update transaction status',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
