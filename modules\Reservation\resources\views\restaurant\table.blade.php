<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $table->branch->name ?? 'Restaurant' }} - Table {{ $table->table_number }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $table->branch->name ?? 'Restaurant' }}</h1>
                        <p class="text-sm text-gray-600">Table {{ $table->table_number }} - {{ $table->area->name ?? 'Main Area' }}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Capacity: {{ $table->seating_capacity }} guests</p>
                        <p class="text-xs text-gray-400">QR Code: {{ $table->qr_code ?? 'Not Generated' }}</p>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Table Information -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="text-center">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">Welcome to Your Table!</h2>
                        <p class="text-lg text-gray-600 mb-8">You're seated at Table {{ $table->table_number }}</p>
                        
                        <!-- Action Buttons -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                            <button onclick="redirectToMenu()" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                                View Menu
                            </button>
                            <button onclick="redirectToOrder()" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                                Place Order
                            </button>
                            <button onclick="callWaiter()" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                                Call Waiter
                            </button>
                        </div>
                    </div>

                    <!-- Table Information -->
                    <div class="mt-12 border-t pt-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Table Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Location Details</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li><strong>Branch:</strong> {{ $table->branch->name ?? 'N/A' }}</li>
                                    <li><strong>Area:</strong> {{ $table->area->name ?? 'N/A' }}</li>
                                    <li><strong>Table Number:</strong> {{ $table->table_number }}</li>
                                    <li><strong>Seating Capacity:</strong> {{ $table->seating_capacity }} guests</li>
                                    <li><strong>Status:</strong> <span class="capitalize">{{ $table->status }}</span></li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Quick Actions</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Scan QR code to access menu</li>
                                    <li>• Use the buttons above for quick actions</li>
                                    <li>• Call staff if you need assistance</li>
                                    <li>• Enjoy your dining experience!</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QR Code Section -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Table QR Code</h3>
                    
                    @if($table->qr_code)
                        <div class="text-center">
                            <div id="qr-code-container" class="mb-4">
                                <canvas id="qr-canvas" class="mx-auto border rounded-md"></canvas>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">QR Code: {{ $table->qr_code }}</p>
                            <div class="space-y-2">
                                <button onclick="downloadQR()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition duration-200">
                                    Download QR Code
                                </button>
                                <button onclick="regenerateQR()" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-md transition duration-200">
                                    Regenerate QR Code
                                </button>
                            </div>
                        </div>
                    @else
                        <div class="text-center">
                            <div class="text-gray-400 mb-4">
                                <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M12 8h4.01M8 12h.01M16 8h.01M8 16h.01M8 8h.01M12 16h.01"></path>
                                </svg>
                            </div>
                            <p class="text-gray-600 mb-4">No QR code generated for this table</p>
                            <button onclick="generateQR()" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition duration-200">
                                Generate QR Code
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-8 mt-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <p class="text-sm">&copy; {{ date('Y') }} {{ $table->branch->name ?? 'Restaurant' }}. All rights reserved.</p>
                <p class="text-xs text-gray-400 mt-1">Powered by EPSIS Restaurant Management System</p>
            </div>
        </footer>
    </div>

    <script>
        // CSRF token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        const tableId = {{ $table->id }};
        const tableQRCode = '{{ $table->qr_code }}';
        const tenantSlug = '{{ $table->branch->tenant->code ?? strtolower(str_replace([" ", "-", "_"], "", $table->branch->tenant->name ?? "restaurant")) }}';

        // Generate QR code on page load if it exists
        $(document).ready(function() {
            if (tableQRCode) {
                generateQRCodeImage();
            }
        });

        function generateQRCodeImage() {
            const canvas = document.getElementById('qr-canvas');
            if (!canvas) return;

            // Generate the QR code URL using the tenant slug and QR code hash
            const qrUrl = `${window.location.origin}/restaurant/${tenantSlug}/${tableQRCode}`;
            
            QRCode.toCanvas(canvas, qrUrl, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('Error generating QR code:', error);
                    $('#qr-code-container').html('<p class="text-red-500">Error generating QR code</p>');
                }
            });
        }

        function generateQR() {
            $.ajax({
                url: `/api/reservation/tables/${tableId}/generate-qr`,
                method: 'POST',
                data: {
                    type: 'table',
                    size: 200
                },
                success: function(response) {
                    if (response.success) {
                        // Update the table's QR code and reload the page
                        location.reload();
                    } else {
                        alert('Error generating QR code: ' + response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    alert('Error generating QR code: ' + (response?.message || 'Unknown error'));
                }
            });
        }

        function regenerateQR() {
            if (confirm('Are you sure you want to regenerate the QR code? The old QR code will no longer work.')) {
                generateQR();
            }
        }

        function downloadQR() {
            const canvas = document.getElementById('qr-canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = `table-${tableId}-qr.png`;
                link.href = canvas.toDataURL();
                link.click();
            }
        }

        function redirectToMenu() {
            if (tableQRCode) {
                window.location.href = `/restaurant/${tenantSlug}/${tableQRCode}?action=menu`;
            } else {
                alert('QR code not available. Please generate a QR code first.');
            }
        }

        function redirectToOrder() {
            if (tableQRCode) {
                window.location.href = `/restaurant/${tenantSlug}/${tableQRCode}?action=order`;
            } else {
                alert('QR code not available. Please generate a QR code first.');
            }
        }

        function callWaiter() {
            // This would integrate with the waiter request system
            alert('Waiter request functionality would be integrated here.');
        }
    </script>
</body>
</html>