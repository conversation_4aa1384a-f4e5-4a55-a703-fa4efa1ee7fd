@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/amazeui-datetimepicker/css/amazeui.datetimepicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/pickerjs/picker.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الاشتراكات / إضافة اشتراك جديد</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('subscriptions-web.index') }}" class="btn btn-secondary">
                <i class="mdi mdi-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-lg-12 col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="main-content-label mg-b-5">
                    إضافة اشتراك جديد
                </div>
                <p class="mg-b-20">قم بملء البيانات التالية لإضافة اشتراك جديد</p>
                
                <form action="{{ route('subscriptions-web.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- Tenant Selection -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tenant_id">المستأجر <span class="text-danger">*</span></label>
                                <select class="form-control select2 @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id" required>
                                    <option value="">اختر المستأجر</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name }} - {{ $tenant->email }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- Plan Selection -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="plan_id">الباقة <span class="text-danger">*</span></label>
                                <select class="form-control select2 @error('plan_id') is-invalid @enderror" 
                                        id="plan_id" name="plan_id" required>
                                    <option value="">اختر الباقة</option>
                                    @foreach($plans as $plan)
                                        <option value="{{ $plan->id }}" 
                                                data-price="{{ $plan->price }}" 
                                                data-billing="{{ $plan->billing_cycle }}"
                                                {{ old('plan_id') == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->name }} - {{ number_format($plan->price, 2) }} ر.س ({{ $plan->billing_cycle == 'monthly' ? 'شهري' : ($plan->billing_cycle == 'yearly' ? 'سنوي' : 'مدى الحياة') }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('plan_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                       id="start_date" name="start_date" value="{{ old('start_date', date('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date">تاريخ النهاية</label>
                                <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                       id="end_date" name="end_date" value="{{ old('end_date') }}" readonly>
                                <small class="text-muted">سيتم حساب تاريخ النهاية تلقائياً بناءً على الباقة المختارة</small>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="amount">المبلغ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('amount') is-invalid @enderror" 
                                           id="amount" name="amount" value="{{ old('amount') }}" required readonly>
                                    <div class="input-group-append">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control select2 @error('status') is-invalid @enderror" 
                                        id="status" name="status">
                                    <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                    <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>معلق</option>
                                    <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_method">طريقة الدفع</label>
                                <select class="form-control select2 @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                                    <option value="paypal" {{ old('payment_method') == 'paypal' ? 'selected' : '' }}>PayPal</option>
                                    <option value="stripe" {{ old('payment_method') == 'stripe' ? 'selected' : '' }}>Stripe</option>
                                    <option value="manual" {{ old('payment_method') == 'manual' ? 'selected' : '' }}>يدوي</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="auto_renew">التجديد التلقائي</label>
                                <select class="form-control select2 @error('auto_renew') is-invalid @enderror" 
                                        id="auto_renew" name="auto_renew">
                                    <option value="0" {{ old('auto_renew', '0') == '0' ? 'selected' : '' }}>لا</option>
                                    <option value="1" {{ old('auto_renew') == '1' ? 'selected' : '' }}>نعم</option>
                                </select>
                                @error('auto_renew')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="4" 
                                          placeholder="أي ملاحظات إضافية">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mg-t-30">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-content-save"></i> حفظ الاشتراك
                        </button>
                        <a href="{{ route('subscriptions-web.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-cancel"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/amazeui-datetimepicker/js/amazeui.datetimepicker.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.js')}}"></script>
<script src="{{URL::asset('assets/plugins/pickerjs/picker.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.js')}}"></script>
<script src="{{URL::asset('assets/js/form-elements.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Calculate end date and amount when plan changes
    $('#plan_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var price = selectedOption.data('price');
        var billingCycle = selectedOption.data('billing');
        var startDate = $('#start_date').val();
        
        if (price) {
            $('#amount').val(price);
        }
        
        if (startDate && billingCycle) {
            calculateEndDate(startDate, billingCycle);
        }
    });
    
    // Calculate end date when start date changes
    $('#start_date').on('change', function() {
        var startDate = $(this).val();
        var selectedOption = $('#plan_id').find('option:selected');
        var billingCycle = selectedOption.data('billing');
        
        if (startDate && billingCycle) {
            calculateEndDate(startDate, billingCycle);
        }
    });
    
    function calculateEndDate(startDate, billingCycle) {
        if (!startDate || !billingCycle) return;
        
        var start = new Date(startDate);
        var end = new Date(start);
        
        switch(billingCycle) {
            case 'monthly':
                end.setMonth(end.getMonth() + 1);
                break;
            case 'yearly':
                end.setFullYear(end.getFullYear() + 1);
                break;
            case 'lifetime':
                // For lifetime, set end date to 10 years from start
                end.setFullYear(end.getFullYear() + 10);
                break;
        }
        
        // Format date as YYYY-MM-DD
        var endDateString = end.getFullYear() + '-' + 
                           String(end.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(end.getDate()).padStart(2, '0');
        
        $('#end_date').val(endDateString);
    }
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var requiredFields = ['tenant_id', 'plan_id', 'start_date', 'amount'];
        
        requiredFields.forEach(function(field) {
            var value = $('#' + field).val();
            if (!value || value.trim() === '') {
                isValid = false;
                $('#' + field).addClass('is-invalid');
            } else {
                $('#' + field).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // Trigger calculation if plan is already selected
    if ($('#plan_id').val()) {
        $('#plan_id').trigger('change');
    }
});
</script>
@endsection