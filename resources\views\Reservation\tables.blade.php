@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
    .table-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .table-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .table-name {
        font-size: 1.2rem;
        font-weight: bold;
        color: #333;
    }
    .table-number {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
    }
    .qr-container {
        text-align: center;
        margin: 15px 0;
    }
    .qr-code-image {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px;
        background: white;
        display: inline-block;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .table-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    .table-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .status-available { background: #d4edda; color: #155724; }
    .status-occupied { background: #f8d7da; color: #721c24; }
    .status-reserved { background: #fff3cd; color: #856404; }
    .status-cleaning { background: #d1ecf1; color: #0c5460; }
    .status-out_of_order { background: #f5c6cb; color: #721c24; }
    .table-capacity {
        color: #666;
        font-size: 0.9rem;
    }
    .view-toggle {
        margin-bottom: 20px;
    }
    .table-actions {
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        justify-content: center;
    }
    .table-actions .btn {
        margin: 2px;
        font-size: 0.8rem;
        padding: 4px 8px;
    }
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة الطاولات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الطاولات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-table-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الطاولات</h4>
                    <div class="view-toggle">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" id="cards-view-btn">
                                <i class="mdi mdi-view-grid"></i> عرض البطاقات
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="table-view-btn">
                                <i class="mdi mdi-table"></i> عرض جدولي
                            </button>
                        </div>
                    </div>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع طاولات المطعم مع رموز QR</p>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-control" id="area-filter">
                            <option value="">جميع المناطق</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="available">متاحة</option>
                            <option value="occupied">مشغولة</option>
                            <option value="reserved">محجوزة</option>
                            <option value="cleaning">تنظيف</option>
                            <option value="out_of_order">خارج الخدمة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" id="capacity-filter" placeholder="الحد الأدنى للسعة" min="1">
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary" id="apply-filters">تطبيق الفلاتر</button>
                        <button type="button" class="btn btn-secondary" id="clear-filters">مسح</button>
                    </div>
                </div>

                <!-- Cards View -->
                <div id="cards-view">
                    <div class="row" id="tables-container">
                        <!-- Tables will be loaded here -->
                    </div>
                </div>

                <!-- Table View -->
                <div id="table-view" style="display: none;">
                    <div class="table-responsive">
                        <table id="tables-table" class="table key-buttons text-md-nowrap">
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">رقم الطاولة</th>
                                    <th class="border-bottom-0">اسم الطاولة</th>
                                    <th class="border-bottom-0">المنطقة</th>
                                    <th class="border-bottom-0">السعة</th>
                                    <th class="border-bottom-0">الحالة</th>
                                    <th class="border-bottom-0">QR Code</th>
                                    <th class="border-bottom-0">QR يدوي</th>
                                    <th class="border-bottom-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add Table Modal -->
<div class="modal fade" id="addTableModal" tabindex="-1" role="dialog" aria-labelledby="addTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTableModalLabel">إضافة طاولة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addTableForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="table_number">رقم الطاولة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="table_number" name="table_number" required maxlength="20">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="table_name">اسم الطاولة</label>
                                <input type="text" class="form-control" id="table_name" name="table_name" maxlength="255">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="area_id">المنطقة <span class="text-danger">*</span></label>
                                <select class="form-control" id="area_id" name="area_id" required>
                                    <option value="">اختر المنطقة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="seating_capacity">السعة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="seating_capacity" name="seating_capacity" required min="1" max="50">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="section">القسم</label>
                                <select class="form-control" id="section" name="section">
                                    <option value="">اختر القسم</option>
                                    <option value="Indoor">داخلي</option>
                                    <option value="Outdoor">خارجي</option>
                                    <option value="VIP">VIP</option>
                                    <option value="Terrace">تراس</option>
                                    <option value="Garden">حديقة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة <span class="text-danger">*</span></label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="available">متاحة</option>
                                    <option value="occupied">مشغولة</option>
                                    <option value="reserved">محجوزة</option>
                                    <option value="cleaning">تنظيف</option>
                                    <option value="out_of_order">خارج الخدمة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="position_x">موقع X (اختياري)</label>
                                <input type="number" class="form-control" id="position_x" name="position_x" step="0.01" placeholder="إحداثي X">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="position_y">موقع Y (اختياري)</label>
                                <input type="number" class="form-control" id="position_y" name="position_y" step="0.01" placeholder="إحداثي Y">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية حول الطاولة"></textarea>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                نشطة
                            </label>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <hr>
                    <h6>إعدادات QR Code</h6>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="generate_qr" name="generate_qr">
                            <label class="form-check-label" for="generate_qr">
                                إنشاء QR Code للطاولة
                            </label>
                        </div>
                    </div>

                    <div id="qr-options" style="display: none;">
                        <div class="form-group">
                            <label>نوع QR Code:</label>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="auto_qr" name="qr_type" value="auto" checked>
                                <label class="form-check-label" for="auto_qr">
                                    إنشاء تلقائي
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="custom_qr" name="qr_type" value="custom">
                                <label class="form-check-label" for="custom_qr">
                                    QR Code مخصص
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="custom-qr-input" style="display: none;">
                            <label for="custom_qr_code">QR Code مخصص:</label>
                            <input type="text" class="form-control" id="custom_qr_code" name="custom_qr_code" maxlength="255">
                            <small class="form-text text-muted">أدخل QR Code مخصص للطاولة</small>
                        </div>
                    </div>

                    <!-- QR Code Preview -->
                    <div id="qr-preview" class="text-center mt-3" style="display: none;">
                        <h6>معاينة QR Code:</h6>
                        <div id="qr-image"></div>
                        <p id="qr-url" class="mt-2 text-muted"></p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="save-table">حفظ</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Manual QR Code Setting Modal -->
<div class="modal fade" id="manualQrModal" tabindex="-1" role="dialog" aria-labelledby="manualQrModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="manualQrModalLabel">تعيين QR Code يدوياً</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="manualQrForm">
                    <div class="form-group">
                        <label for="manual-qr-code">QR Code:</label>
                        <input type="text" class="form-control" id="manual-qr-code" name="qr_code" required>
                        <small class="form-text text-muted">أدخل QR Code مخصص للطاولة</small>
                    </div>
                    <div id="manual-qr-preview" class="text-center mt-3" style="display: none;">
                        <h6>معاينة QR Code:</h6>
                        <div id="manual-qr-image"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info" id="preview-manual-qr">معاينة</button>
                <button type="button" class="btn btn-primary" id="save-manual-qr">حفظ</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    let tablesTable;
    let currentView = 'cards';

    // Initialize
    loadTables();
    loadAreas(); // Load areas for the dropdown

    // Add Table Button Click Event
    $('#add-table-btn').click(function() {
        $('#addTableForm')[0].reset();
        $('#addTableModal').modal('show');
    });

    // Save Table Button Click Event
    $('#save-table').click(function() {
        let formData = {
            table_number: $('#table_number').val(),
            table_name: $('#table_name').val(),
            area_id: $('#area_id').val(),
            seating_capacity: $('#seating_capacity').val(),
            section: $('#section').val(),
            status: $('#status').val(),
            notes: $('#notes').val(),
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            _token: '{{ csrf_token() }}'
        };

        // Handle position coordinates
        let positionX = $('#position_x').val();
        let positionY = $('#position_y').val();
        if (positionX && positionY) {
            formData.position_coordinates = JSON.stringify({
                x: parseFloat(positionX),
                y: parseFloat(positionY)
            });
        }

        // Handle QR code generation
        formData.generate_qr = $('#generate_qr').is(':checked') ? 1 : 0;

        if (formData.generate_qr) {
            formData.qr_type = $('input[name="qr_type"]:checked').val();
            if (formData.qr_type === 'custom') {
                formData.custom_qr_code = $('#custom_qr_code').val();
            }
        }

        // Validate required fields
        if (!formData.table_number || !formData.area_id || !formData.seating_capacity || !formData.status) {
            swal('تنبيه!', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // Validate custom QR code if selected
        if (formData.generate_qr && $('input[name="qr_type"]:checked').val() === 'custom' && !formData.custom_qr_code) {
            swal('تنبيه!', 'يرجى إدخال QR Code مخصص', 'warning');
            return;
        }

        $.post('{{ route("tables.store") }}', formData, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                $('#addTableModal').modal('hide');
                
                // Show QR code if generated
                if (data.qr_data) {
                    setTimeout(() => {
                        showQRResult(data.qr_data);
                    }, 500);
                }
                
                if (currentView === 'cards') {
                    loadTables();
                } else {
                    tablesTable.ajax.reload();
                }
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء إضافة الطاولة';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                let errors = xhr.responseJSON.errors;
                message = Object.values(errors).flat().join('\n');
            }
            swal('خطأ!', message, 'error');
        });
    });

    // QR Code functionality
    $('#generate_qr').change(function() {
        if ($(this).is(':checked')) {
            $('#qr-options').show();
        } else {
            $('#qr-options').hide();
            $('#qr-preview').hide();
        }
    });

    $('input[name="qr_type"]').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom-qr-input').show();
        } else {
            $('#custom-qr-input').hide();
        }
        $('#qr-preview').hide();
    });

    // Preview QR code
    $('#custom_qr_code').on('input', function() {
        let qrCode = $(this).val().trim();
        if (qrCode && $('input[name="qr_type"]:checked').val() === 'custom') {
            previewQRCode(qrCode);
        } else {
            $('#qr-preview').hide();
        }
    });

    function previewQRCode(qrCode) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        $('#qr-image').html(`<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`);
        $('#qr-url').text(qrUrl);
        $('#qr-preview').show();
    }

    function showQRResult(qrData) {
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData.url)}`;
        
        swal({
            title: 'تم إنشاء QR Code بنجاح!',
            content: {
                element: 'div',
                attributes: {
                    innerHTML: `
                        <div class="text-center">
                            <img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background: white; margin: 10px 0;">
                            <p><strong>QR Code:</strong> ${qrData.qr_code}</p>
                            <p><strong>الرابط:</strong></p>
                            <p style="word-break: break-all; font-size: 12px;">${qrData.url}</p>
                        </div>
                    `
                }
            },
            buttons: {
                download: {
                    text: 'تحميل QR Code',
                    value: 'download',
                    className: 'btn-success'
                },
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'download') {
                downloadQR(qrImageUrl, `table-${qrData.table_number}-qr.png`);
            } else if (value === 'copy') {
                navigator.clipboard.writeText(qrData.url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = qrData.url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    }

    // Load areas for dropdown
    function loadAreas() {
        $.get('/reservation/api/areas', function(data) {
            let areaSelect = $('#area_id');
            let areaFilter = $('#area-filter');
            
            areaSelect.empty().append('<option value="">اختر المنطقة</option>');
            areaFilter.empty().append('<option value="">جميع المناطق</option>');
            
            if (data && data.length > 0) {
                data.forEach(function(area) {
                    areaSelect.append(`<option value="${area.id}">${area.name}</option>`);
                    areaFilter.append(`<option value="${area.id}">${area.name}</option>`);
                });
            }
        }).fail(function() {
            console.log('Failed to load areas');
        });
    }

    // View toggle
    $('#cards-view-btn').click(function() {
        currentView = 'cards';
        $('#cards-view').show();
        $('#table-view').hide();
        $(this).addClass('active');
        $('#table-view-btn').removeClass('active');
        loadTables();
    });

    $('#table-view-btn').click(function() {
        currentView = 'table';
        $('#cards-view').hide();
        $('#table-view').show();
        $(this).addClass('active');
        $('#cards-view-btn').removeClass('active');
        initDataTable();
    });

    // Load tables for cards view
    function loadTables() {
        if (currentView !== 'cards') return;

        let filters = {
            area_id: $('#area-filter').val(),
            status: $('#status-filter').val(),
            capacity: $('#capacity-filter').val()
        };

        // Using the controller route
        $.get('{{ route("reservation.tables.data") }}', filters, function(data) {
            renderTablesCards(data.data || data);
        }).fail(function() {
            // Fallback to mock data for testing
            renderTablesCards([
                {
                    id: 1,
                    table_number: 'T001',
                    table_name: 'طاولة رقم 1',
                    seating_capacity: 4,
                    status: 'available',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '436a1f405d08a1a57030802e561811ae'
                },
                {
                    id: 2,
                    table_number: 'T002',
                    table_name: 'طاولة رقم 2',
                    seating_capacity: 6,
                    status: 'occupied',
                    area_name: 'الصالة الرئيسية',
                    qr_code: '536a1f405d08a1a57030802e561811af'
                }
            ]);
        });
    }

    // Render tables as cards
    function renderTablesCards(tables) {
        let container = $('#tables-container');
        container.empty();

        if (!tables || tables.length === 0) {
            container.append('<div class="col-12"><p class="text-center">لا توجد طاولات</p></div>');
            return;
        }

        tables.forEach(function(table) {
            let statusClass = `status-${table.status}`;
            let statusText = getStatusText(table.status);
            
            let qrCodeUrl = generateTableUrl(table);
            let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrCodeUrl)}`;
            
            let card = `
                <div class="col-md-6 col-lg-4">
                    <div class="table-card">
                        <div class="table-header">
                            <div>
                                <div class="table-name">${table.table_name || table.table_number}</div>
                                <span class="table-number">طاولة ${table.table_number}</span>
                            </div>
                            <span class="table-status ${statusClass}">${statusText}</span>
                        </div>
                        
                        <div class="qr-container">
                            <img src="${qrImageUrl}" alt="QR Code" class="qr-code-image" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">
                        </div>
                        
                        <div class="table-info">
                            <span class="table-capacity">
                                <i class="fas fa-users"></i> ${table.seating_capacity} أشخاص
                            </span>
                            <span class="text-muted">${table.area_name || 'غير محدد'}</span>
                        </div>
                        
                        <div class="table-actions">
                            <button class="btn btn-sm btn-info" onclick="showTableUrl('${qrCodeUrl}')" title="عرض الرابط">
                                <i class="fas fa-link"></i> الرابط
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="generateNewQR(${table.id})" title="إنشاء QR جديد">
                                <i class="fas fa-sync"></i> تجديد
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="showManualQRModal(${table.id})" title="تعيين QR يدوياً">
                                <i class="fas fa-edit"></i> يدوي
                            </button>
                            <button class="btn btn-sm btn-success" onclick="downloadQR('${qrImageUrl}', 'table-${table.table_number}-qr.png')" title="تحميل QR">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.append(card);
        });
    }

    // Generate table URL
    function generateTableUrl(table) {
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug(); // Get from global or config
        return `${baseUrl}/restaurant/table/${table.qr_code || table.id}?hash=${tenantSlug}`;
    }

    // Get tenant slug (this should be available globally or from config)
    function getTenantSlug() {
        // Get tenant slug from authenticated user's branch tenant
        @if(auth()->user() && auth()->user()->branch && auth()->user()->branch->tenant)
            @php
                $tenant = auth()->user()->branch->tenant;
                $tenantSlug = $tenant->code ?? strtolower(str_replace([' ', '-', '_'], '', $tenant->name));
            @endphp
            return '{{ $tenantSlug }}';
        @else
            return 'default-tenant'; // Default fallback
        @endif
    }

    // Generate QR Code (removed - now using QR Server API)

    // Show table URL in a modal or alert
    window.showTableUrl = function(url) {
        swal({
            title: 'رابط الطاولة',
            text: url,
            icon: 'info',
            buttons: {
                copy: {
                    text: 'نسخ الرابط',
                    value: 'copy',
                    className: 'btn-primary'
                },
                close: {
                    text: 'إغلاق',
                    value: 'close',
                    className: 'btn-secondary'
                }
            }
        }).then((value) => {
            if (value === 'copy') {
                // Copy to clipboard
                navigator.clipboard.writeText(url).then(() => {
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    let textArea = document.createElement('textarea');
                    textArea.value = url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    swal('تم!', 'تم نسخ الرابط إلى الحافظة', 'success');
                });
            }
        });
    };

    // Download QR code
    window.downloadQR = function(qrImageUrl, filename) {
        let link = document.createElement('a');
        link.href = qrImageUrl;
        link.download = filename || 'qr-code.png';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Get status text in Arabic
    function getStatusText(status) {
        const statusMap = {
            'available': 'متاحة',
            'occupied': 'مشغولة',
            'reserved': 'محجوزة',
            'cleaning': 'تنظيف',
            'out_of_order': 'خارج الخدمة'
        };
        return statusMap[status] || status;
    }

    // Initialize DataTable for table view
    function initDataTable() {
        if (tablesTable) {
            tablesTable.destroy();
        }

        tablesTable = $('#tables-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("reservation.tables.data") }}',
                data: function(d) {
                    d.area_id = $('#area-filter').val();
                    d.status = $('#status-filter').val();
                    d.capacity = $('#capacity-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'table_number', name: 'table_number' },
                { data: 'table_name', name: 'table_name' },
                { data: 'area_name', name: 'area_name' },
                { data: 'seating_capacity', name: 'seating_capacity' },
                { 
                    data: 'status', 
                    name: 'status',
                    render: function(data, type, row) {
                        let statusClass = `status-${data}`;
                        let statusText = getStatusText(data);
                        return `<span class="table-status ${statusClass}">${statusText}</span>`;
                    }
                },
                { 
                    data: 'qr_code_display', 
                    name: 'qr_code_display',
                    orderable: false,
                    searchable: false
                },
                { 
                    data: 'action', 
                    name: 'action', 
                    orderable: false, 
                    searchable: false
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            }
        });
    }

    // Filters
    $('#apply-filters').click(function() {
        if (currentView === 'cards') {
            loadTables();
        } else {
            tablesTable.ajax.reload();
        }
    });

    $('#clear-filters').click(function() {
        $('#area-filter, #status-filter').val('');
        $('#capacity-filter').val('');
        if (currentView === 'cards') {
            loadTables();
        } else {
            tablesTable.ajax.reload();
        }
    });

    // Generate new QR code
    window.generateNewQR = function(tableId) {
        if (confirm('هل أنت متأكد من إنشاء QR Code جديد لهذه الطاولة؟')) {
            $.post(`{{ url('tables') }}/${tableId}/regenerate-qr`, {
                _token: '{{ csrf_token() }}'
            }, function(data) {
                if (data.success) {
                    swal('نجح!', data.message, 'success');
                    if (currentView === 'cards') {
                        loadTables();
                    } else {
                        tablesTable.ajax.reload();
                    }
                }
            }).fail(function(xhr) {
                let message = 'حدث خطأ أثناء إنشاء QR Code جديد';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                swal('خطأ!', message, 'error');
            });
        }
    };

    // Show manual QR modal
    window.showManualQRModal = function(tableId) {
        $('#manualQrModal').data('table-id', tableId);
        $('#manual-qr-code').val('');
        $('#manual-qr-preview').hide();
        $('#manualQrModal').modal('show');
    };

    // Preview manual QR code
    $('#preview-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        let tableId = $('#manualQrModal').data('table-id');
        let baseUrl = window.location.origin;
        let tenantSlug = getTenantSlug();
        let qrUrl = `${baseUrl}/restaurant/table/${qrCode}?hash=${tenantSlug}`;

        // Generate QR code preview using QR Server API
        let qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrUrl)}`;
        
        let container = document.getElementById('manual-qr-image');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code Preview" style="width: 150px; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px; background: white;">`;
        $('#manual-qr-preview').show();
    });

    // Save manual QR code
    $('#save-manual-qr').click(function() {
        let qrCode = $('#manual-qr-code').val().trim();
        let tableId = $('#manualQrModal').data('table-id');
        
        if (!qrCode) {
            swal('تنبيه!', 'يرجى إدخال QR Code أولاً', 'warning');
            return;
        }

        $.post(`{{ url('tables') }}/${tableId}/set-manual-qr`, {
            _token: '{{ csrf_token() }}',
            qr_code: qrCode
        }, function(data) {
            if (data.success) {
                swal('نجح!', data.message, 'success');
                $('#manualQrModal').modal('hide');
                if (currentView === 'cards') {
                    loadTables();
                } else {
                    tablesTable.ajax.reload();
                }
            }
        }).fail(function(xhr) {
            let message = 'حدث خطأ أثناء حفظ QR Code';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            swal('خطأ!', message, 'error');
        });
    });

    // Show QR Modal with larger image
    window.showQRModal = function(qrImageUrl, qrUrl, qrCode) {
        $('#qrModalLabel').text(`QR Code - ${qrCode}`);
        $('#qr-table-info').html(`<h6>QR Code: ${qrCode}</h6>`);
        $('#qr-url-info').html(`<small class="text-muted">${qrUrl}</small>`);
        
        // Display the QR image
        let container = document.getElementById('qr-code-container');
        container.innerHTML = `<img src="${qrImageUrl}" alt="QR Code" style="width: 200px; height: 200px;" class="img-thumbnail">`;
        
        // Store for download
        $('#download-qr').data('qr-url', qrImageUrl);
        $('#download-qr').data('filename', `qr-code-${qrCode}.png`);
        
        $('#qrModal').modal('show');
    };
});
</script>
@endsection