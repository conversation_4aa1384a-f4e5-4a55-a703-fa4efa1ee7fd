@extends('layouts.master')

@section('css')
<!-- DataTable css -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الاشتراكات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('subscriptions.create') }}" class="btn btn-primary">
                <i class="mdi mdi-plus"></i> إضافة اشتراك جديد
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الاشتراكات</h4>
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#createSubscriptionModal">
                        <i class="mdi mdi-plus"></i> إضافة اشتراك جديد
                    </button>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع اشتراكات المستأجرين</p>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-control select2" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                            <option value="cancelled">ملغي</option>
                            <option value="expired">منتهي الصلاحية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control select2" id="plan-filter">
                            <option value="">جميع الباقات</option>
                            @foreach($plans as $plan)
                                <option value="{{ $plan->id }}">{{ $plan->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-secondary" id="filter-btn">
                            <i class="mdi mdi-filter"></i> تصفية
                        </button>
                        <button type="button" class="btn btn-light" id="reset-filter">
                            <i class="mdi mdi-refresh"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="subscriptions-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">المستأجر</th>
                                <th class="border-bottom-0">الباقة</th>
                                <th class="border-bottom-0">السعر</th>
                                <th class="border-bottom-0">دورة الفوترة</th>
                                <th class="border-bottom-0">تاريخ البداية</th>
                                <th class="border-bottom-0">تاريخ الانتهاء</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscriptions as $subscription)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ $subscription->tenant->name }}</h6>
                                            <small class="text-muted">{{ $subscription->tenant->contact_email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ $subscription->subscriptionPlan->name }}</span>
                                </td>
                                <td>{{ number_format($subscription->subscriptionPlan->price, 2) }} ر.س</td>
                                <td>
                                    @if($subscription->subscriptionPlan->billing_cycle == 'monthly')
                                        شهري
                                    @elseif($subscription->subscriptionPlan->billing_cycle == 'yearly')
                                        سنوي
                                    @else
                                        مدى الحياة
                                    @endif
                                </td>
                                <td>{{ $subscription->start_date ? $subscription->start_date->format('Y-m-d') : '-' }}</td>
                                <td>{{ $subscription->end_date ? $subscription->end_date->format('Y-m-d') : '-' }}</td>
                                <td>
                                    @if($subscription->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @elseif($subscription->status == 'inactive')
                                        <span class="badge badge-secondary">غير نشط</span>
                                    @elseif($subscription->status == 'suspended')
                                        <span class="badge badge-warning">معلق</span>
                                    @elseif($subscription->status == 'cancelled')
                                        <span class="badge badge-danger">ملغي</span>
                                    @else
                                        <span class="badge badge-dark">منتهي الصلاحية</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-info btn-sm" onclick="showSubscription({{ $subscription->id }})" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="editSubscription({{ $subscription->id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        @if($subscription->status == 'active')
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="suspendSubscription({{ $subscription->id }})" title="تعليق">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        @elseif($subscription->status == 'suspended')
                                            <button type="button" class="btn btn-success btn-sm" onclick="reactivateSubscription({{ $subscription->id }})" title="إعادة تفعيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        @endif
                                        @if($subscription->status != 'cancelled')
                                            <button type="button" class="btn btn-danger btn-sm" onclick="cancelSubscription({{ $subscription->id }})" title="إلغاء">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-primary btn-sm" onclick="upgradeSubscription({{ $subscription->id }})" title="ترقية">
                                            <i class="fas fa-arrow-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteSubscription({{ $subscription->id }}, '{{ $subscription->tenant->name }} - {{ $subscription->subscriptionPlan->name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $subscriptions->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->
@endsection

@section('js')
<!-- Internal Data tables -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>

@include('tenant::subscriptions.modals')

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });
    
    // Initialize DataTable
    $('#subscriptions-table').DataTable({
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        pageLength: 25,
        order: [[ 0, "desc" ]],
        columnDefs: [
            { orderable: false, targets: -1 } // Disable ordering on last column (actions)
        ]
    });
    
    // Filter functionality
    $('#filter-btn').click(function() {
        var status = $('#status-filter').val();
        var planId = $('#plan-filter').val();
        
        var url = new URL(window.location.href);
        if (status) {
            url.searchParams.set('status', status);
        } else {
            url.searchParams.delete('status');
        }
        
        if (planId) {
            url.searchParams.set('plan_id', planId);
        } else {
            url.searchParams.delete('plan_id');
        }
        
        window.location.href = url.toString();
    });
    
    // Reset filter
    $('#reset-filter').click(function() {
        var url = new URL(window.location.href);
        url.searchParams.delete('status');
        url.searchParams.delete('plan_id');
        window.location.href = url.toString();
    });

    // Plan selection change event for create modal
    $('#create_plan_id').change(function() {
        var selectedOption = $(this).find('option:selected');
        var price = selectedOption.data('price');
        var billing = selectedOption.data('billing');
        
        if (price) {
            $('#create_amount').val(price);
            
            // Calculate end date based on billing cycle
            var startDate = new Date($('#create_start_date').val());
            var endDate = new Date(startDate);
            
            switch(billing) {
                case 'monthly':
                    endDate.setMonth(endDate.getMonth() + 1);
                    break;
                case 'quarterly':
                    endDate.setMonth(endDate.getMonth() + 3);
                    break;
                case 'semi_annual':
                    endDate.setMonth(endDate.getMonth() + 6);
                    break;
                case 'annual':
                    endDate.setFullYear(endDate.getFullYear() + 1);
                    break;
            }
            
            $('#create_end_date').val(endDate.toISOString().split('T')[0]);
        }
    });

    // Start date change event for create modal
    $('#create_start_date').change(function() {
        $('#create_plan_id').trigger('change');
    });
});

function showSubscription(id) {
    $.get(`/admin/subscriptions-web/${id}`, function(data) {
        $('#show_tenant_name').text(data.tenant.name + ' - ' + data.tenant.contact_email);
        $('#show_plan_name').text(data.subscription_plan.name);
        $('#show_start_date').text(data.start_date);
        $('#show_end_date').text(data.end_date || 'غير محدد');
        $('#show_amount').text(data.total_amount + ' ر.س');
        $('#show_status').html(getStatusBadge(data.status));
        $('#show_payment_method').text(getPaymentMethodText(data.payment_method));
        $('#show_auto_renew').text(data.auto_renew ? 'نعم' : 'لا');
        $('#show_notes').text(data.notes || 'لا توجد ملاحظات');
        $('#show_created_at').text(new Date(data.created_at).toLocaleDateString('ar-SA'));
        $('#show_updated_at').text(new Date(data.updated_at).toLocaleDateString('ar-SA'));
        
        // Store subscription ID for edit from show modal
        $('#showSubscriptionModal').data('subscription-id', id);
        $('#showSubscriptionModal').modal('show');
    }).fail(function() {
        alert('حدث خطأ في تحميل بيانات الاشتراك');
    });
}

function editSubscription(id) {
    $.get(`/admin/subscriptions-web/${id}`, function(data) {
        $('#editSubscriptionForm').attr('action', `/admin/subscriptions-web/${id}`);
        $('#edit_tenant_id').val(data.tenant_id).trigger('change');
        $('#edit_plan_id').val(data.plan_id).trigger('change');
        $('#edit_start_date').val(data.start_date);
        $('#edit_end_date').val(data.end_date);
        $('#edit_amount').val(data.total_amount);
        $('#edit_status').val(data.status);
        $('#edit_payment_method').val(data.payment_method);
        $('#edit_auto_renew').val(data.auto_renew ? '1' : '0');
        $('#edit_notes').val(data.notes);
        
        $('#editSubscriptionModal').modal('show');
    }).fail(function() {
        alert('حدث خطأ في تحميل بيانات الاشتراك للتعديل');
    });
}

function deleteSubscription(id, details) {
    $('#delete_subscription_details').text(details);
    $('#deleteSubscriptionForm').attr('action', `/admin/subscriptions-web/${id}`);
    $('#deleteSubscriptionModal').modal('show');
}

function suspendSubscription(id) {
    if (confirm('هل أنت متأكد من إيقاف هذا الاشتراك؟')) {
        $.ajax({
            url: `/admin/subscriptions-web/${id}/suspend`,
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                alert('تم إيقاف الاشتراك بنجاح');
                location.reload();
            },
            error: function() {
                alert('حدث خطأ في إيقاف الاشتراك');
            }
        });
    }
}

function reactivateSubscription(id) {
    if (confirm('هل أنت متأكد من إعادة تفعيل هذا الاشتراك؟')) {
        $.ajax({
            url: `/admin/subscriptions-web/${id}/reactivate`,
            type: 'PATCH',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                location.reload();
            }
        });
    }
}

function cancelSubscription(id) {
    if (confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟')) {
        $.ajax({
            url: `/admin/subscriptions-web/${id}/cancel`,
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                alert('تم إلغاء الاشتراك بنجاح');
                location.reload();
            },
            error: function() {
                alert('حدث خطأ في إلغاء الاشتراك');
            }
        });
    }
}

function upgradeSubscription(id) {
    window.location.href = `/admin/subscriptions-web/${id}/upgrade`;
}

function reactivateSubscription(id) {
    if (confirm('هل أنت متأكد من إعادة تفعيل هذا الاشتراك؟')) {
        $.ajax({
            url: `/admin/subscriptions-web/${id}/reactivate`,
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                alert('تم إعادة تفعيل الاشتراك بنجاح');
                location.reload();
            },
            error: function() {
                alert('حدث خطأ في إعادة تفعيل الاشتراك');
            }
        });
    }
}

function deleteSubscription(id, name) {
    if (confirm(`هل أنت متأكد من حذف اشتراك ${name}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        $.ajax({
            url: `/admin/subscriptions-web/${id}`,
            type: 'DELETE',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                alert('تم حذف الاشتراك بنجاح');
                location.reload();
            },
            error: function() {
                alert('حدث خطأ في حذف الاشتراك');
            }
        });
    }
}

function getStatusBadge(status) {
    const statusMap = {
        'active': '<span class="badge badge-success">نشط</span>',
        'pending': '<span class="badge badge-warning">في الانتظار</span>',
        'suspended': '<span class="badge badge-secondary">معلق</span>',
        'cancelled': '<span class="badge badge-danger">ملغي</span>'
    };
    return statusMap[status] || status;
}

function getPaymentMethodText(method) {
    const methodMap = {
        'cash': 'نقدي',
        'bank_transfer': 'تحويل بنكي',
        'credit_card': 'بطاقة ائتمان',
        'online': 'دفع إلكتروني'
    };
    return methodMap[method] || method || 'غير محدد';
}

// Edit from show modal
$('#editFromShow').click(function() {
    $('#showSubscriptionModal').modal('hide');
    // Get the subscription ID from the show modal and trigger edit
    var subscriptionId = $('#showSubscriptionModal').data('subscription-id');
    if (subscriptionId) {
        editSubscription(subscriptionId);
    }
});
</script>
@endsection