@extends('layouts.master')

@section('title', 'ترقية الاشتراك')

@section('css')
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
@endsection

@section('content')
<div class="container-fluid">
    <div class="row row-sm">
        <div class="col-xl-12">
            <div class="card mg-b-20">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between">
                        <h4 class="card-title mg-b-0">ترقية الاشتراك</h4>
                        <a href="{{ route('subscriptions-web.index') }}" class="btn btn-secondary btn-sm">
                            <i class="mdi mdi-arrow-left"></i> العودة
                        </a>
                    </div>
                    <p class="tx-12 tx-gray-500 mb-2">ترقية اشتراك {{ $subscription->tenant->name }}</p>
                </div>
                <div class="card-body">
                    <!-- Current Subscription Info -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6 class="mb-2">الاشتراك الحالي:</h6>
                                <p class="mb-1"><strong>الباقة:</strong> {{ $subscription->subscriptionPlan->name }}</p>
                                <p class="mb-1"><strong>السعر:</strong> {{ $subscription->total_amount }} ر.س</p>
                                <p class="mb-1"><strong>تاريخ الانتهاء:</strong> {{ $subscription->end_date ? $subscription->end_date->format('Y-m-d') : 'غير محدد' }}</p>
                                <p class="mb-0"><strong>الحالة:</strong> 
                                    @if($subscription->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @elseif($subscription->status == 'suspended')
                                        <span class="badge badge-warning">معلق</span>
                                    @else
                                        <span class="badge badge-secondary">{{ $subscription->status }}</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Upgrade Form -->
                    <form action="{{ route('subscriptions-web.upgrade', $subscription->id) }}" method="POST" id="upgradeForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="new_plan_id">الباقة الجديدة <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="new_plan_id" id="new_plan_id" required>
                                        <option value="">اختر الباقة الجديدة</option>
                                        @foreach($plans as $plan)
                                            <option value="{{ $plan->id }}" 
                                                    data-price="{{ $plan->price }}" 
                                                    data-billing="{{ $plan->billing_cycle }}"
                                                    data-features="{{ $plan->features }}">
                                                {{ $plan->name }} - {{ $plan->price }} ر.س ({{ $plan->billing_cycle == 'monthly' ? 'شهري' : ($plan->billing_cycle == 'yearly' ? 'سنوي' : 'مدى الحياة') }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('new_plan_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="upgrade_type">نوع الترقية</label>
                                    <select class="form-control" name="upgrade_type" id="upgrade_type">
                                        <option value="immediate">فوري</option>
                                        <option value="next_billing">في الفترة القادمة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="prorate">حساب التناسب</label>
                                    <select class="form-control" name="prorate" id="prorate">
                                        <option value="1">نعم - احسب الفرق</option>
                                        <option value="0">لا - السعر كامل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_method">طريقة الدفع</label>
                                    <select class="form-control" name="payment_method" id="payment_method">
                                        <option value="credit_card">بطاقة ائتمان</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="cash">نقدي</option>
                                        <option value="check">شيك</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes">ملاحظات</label>
                                    <textarea class="form-control" name="notes" id="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Price Calculation -->
                        <div class="row" id="priceCalculation" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <h6 class="mb-2">حساب التكلفة:</h6>
                                    <p class="mb-1"><span id="currentPlanPrice">السعر الحالي: {{ $subscription->total_amount }} ر.س</span></p>
                                    <p class="mb-1"><span id="newPlanPrice">السعر الجديد: - ر.س</span></p>
                                    <p class="mb-1"><span id="proratedAmount">المبلغ المتناسب: - ر.س</span></p>
                                    <hr>
                                    <p class="mb-0"><strong><span id="totalAmount">المبلغ الإجمالي: - ر.س</span></strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-arrow-up"></i> ترقية الاشتراك
                            </button>
                            <a href="{{ route('subscriptions-web.index') }}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر...',
        allowClear: true
    });

    // Calculate price when plan changes
    $('#new_plan_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var newPrice = selectedOption.data('price');
        var currentPrice = {{ $subscription->total_amount }};
        
        if (newPrice) {
            $('#newPlanPrice').text('السعر الجديد: ' + newPrice + ' ر.س');
            
            var prorate = $('#prorate').val() == '1';
            var upgradeType = $('#upgrade_type').val();
            
            if (prorate && upgradeType == 'immediate') {
                // Simple calculation - in real implementation, this would be more complex
                var difference = newPrice - currentPrice;
                $('#proratedAmount').text('الفرق: ' + difference + ' ر.س');
                $('#totalAmount').text('المبلغ الإجمالي: ' + Math.max(0, difference) + ' ر.س');
            } else {
                $('#proratedAmount').text('المبلغ المتناسب: ' + newPrice + ' ر.س');
                $('#totalAmount').text('المبلغ الإجمالي: ' + newPrice + ' ر.س');
            }
            
            $('#priceCalculation').show();
        } else {
            $('#priceCalculation').hide();
        }
    });

    // Recalculate when prorate or upgrade type changes
    $('#prorate, #upgrade_type').on('change', function() {
        $('#new_plan_id').trigger('change');
    });

    // Form submission
    $('#upgradeForm').on('submit', function(e) {
        e.preventDefault();
        
        if (!$('#new_plan_id').val()) {
            alert('يرجى اختيار الباقة الجديدة');
            return;
        }

        if (confirm('هل أنت متأكد من ترقية هذا الاشتراك؟')) {
            this.submit();
        }
    });
});
</script>
@endsection