<?php

namespace App\Http\Requests\Menu;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'category_id' => 'nullable|exists:menu_categories,id',
            'name' => 'sometimes|string|max:255',
            'code' => 'sometimes|string|max:50',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string',
            'base_price' => 'sometimes|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'string|max:500',
            'prep_time_minutes' => 'integer|min:0',
            'calories' => 'nullable|integer|min:0',
            'nutritional_info' => 'nullable|array',
            'allergens' => 'nullable|array',
            'dietary_info' => 'nullable|array',
            'recipe_id' => 'nullable|exists:recipes,id',
            'barcode' => 'nullable|string|max:100',
            'sku' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|between:1,5',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'base_price.min' => 'Base price must be at least 0',
            'spice_level.between' => 'Spice level must be between 1 and 5',
        ];
    }
}
