<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Order;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;
use Modules\Kitchen\Models\KotOrder;

echo "=== KOT Creation Test ===\n";

// Get a confirmed order with items
$order = Order::where('status', 'confirmed')
    ->whereHas('items')
    ->with(['items.menuItem', 'branch'])
    ->first();

if (!$order) {
    echo "No confirmed orders with items found\n";
    exit;
}

echo "Order: {$order->order_number}\n";
echo "Items: {$order->items->count()}\n";
echo "Branch: {$order->branch->name}\n";

// Check kitchen assignments for each item
foreach ($order->items as $item) {
    echo "\nItem: {$item->menuItem->name}\n";
    $kitchenAssignments = KitchenMenuItem::where('menu_item_id', $item->menu_item_id)
        ->whereHas('kitchen', function($q) use ($order) {
            $q->where('branch_id', $order->branch_id);
        })
        ->with('kitchen')
        ->get();
    
    echo "Kitchen assignments: {$kitchenAssignments->count()}\n";
    foreach ($kitchenAssignments as $assignment) {
        echo "  - Kitchen: {$assignment->kitchen->name}\n";
    }
}

// Check existing KOTs for this order
$existingKots = KotOrder::where('order_id', $order->id)->get();
echo "\nExisting KOTs: {$existingKots->count()}\n";

// Try to create KOT manually
echo "\n=== Manual KOT Creation Test ===\n";
try {
    $kotCount = $order->createKotOrders();
    echo "Created {$kotCount} KOT(s)\n";
    
    // Check KOTs again
    $newKots = KotOrder::where('order_id', $order->id)->with('kitchen')->get();
    echo "Total KOTs now: {$newKots->count()}\n";
    foreach ($newKots as $kot) {
        echo "  - KOT #{$kot->kot_number} for {$kot->kitchen->name} (Status: {$kot->status})\n";
    }
} catch (Exception $e) {
    echo "Error creating KOT: {$e->getMessage()}\n";
    echo "Stack trace:\n{$e->getTraceAsString()}\n";
}